import 'package:flutter/material.dart';
import 'main_navigation_page.dart';
import 'biometric_auth_page.dart';
import '../services/biometric_auth_service.dart';
import '../services/sync_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _textController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<Offset> _textSlideAnimation;

  @override
  void initState() {
    super.initState();

    // Configuration des contrôleurs d'animation
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Configuration des animations
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _textFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeOutBack),
    );

    _startAnimations();
  }

  void _startAnimations() async {
    // Démarrer l'animation de fade
    _fadeController.forward();

    // Attendre un peu puis démarrer le zoom
    await Future.delayed(const Duration(milliseconds: 300));
    _scaleController.forward();

    // Attendre puis démarrer l'animation du texte
    await Future.delayed(const Duration(milliseconds: 800));
    _textController.forward();

    // Attendre la fin des animations puis naviguer
    await Future.delayed(const Duration(milliseconds: 2500));
    _navigateToDashboard();
  }

  // Méthode pour démarrer la synchronisation en arrière-plan
  void _startBackgroundSync(SyncService syncService) async {
    // Exécuter dans un Future.microtask pour ne pas bloquer l'interface
    Future.microtask(() async {
      try {
        // Vérifier si le service est initialisé, sinon l'initialiser en arrière-plan
        if (!syncService.isInitialized()) {
          syncService.initializeInBackground();
          // Retourner car l'initialisation en arrière-plan s'occupera de la synchronisation
          return;
        }
        
        // Si le service est déjà initialisé, vérifier si la migration initiale est terminée
        final bool initialMigrationDone = await syncService.isInitialMigrationDone();
        if (!initialMigrationDone && !syncService.isSyncing()) {
          // Synchroniser les données en arrière-plan
          await syncService.syncNow();
        }
      } catch (e) {
        debugPrint('Erreur lors de la synchronisation en arrière-plan: $e');
      }
    });
  }

  void _navigateToDashboard() async {
    if (!mounted) return;

    final BiometricAuthService biometricService = BiometricAuthService();
    final SyncService syncService = SyncService.instance;
    
    // Vérifier si c'est le premier lancement et configurer l'authentification biométrique
    await biometricService.setupBiometricOnFirstLaunch(context);
    
    if (!mounted) return;
    
    // Vérifier si l'authentification biométrique est activée
    final bool isBiometricEnabled = await biometricService.isBiometricEnabled();
    
    Widget nextPage;
    if (isBiometricEnabled) {
      nextPage = const BiometricAuthPage();
    } else {
      nextPage = const MainNavigationPage();
    }
    
    if (!mounted) return;
    
    // Lancer la synchronisation en arrière-plan sans bloquer l'interface
    _startBackgroundSync(syncService);
    
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => nextPage,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(
            begin: begin,
            end: end,
          ).chain(CurveTween(curve: curve));

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo animé
              AnimatedBuilder(
                animation: _scaleController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: Transform.scale(
                      scale: _scaleAnimation.value,
                      child: SizedBox(
                        width: 200,
                        height: 200,
                        child: Image.asset(
                          'assets/images/logo_entreprise.png',
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            // Fallback si l'image n'est pas trouvée
                            return Container(
                              color: Colors.white,
                              child: const Icon(
                                Icons.business,
                                size: 100,
                                color: Colors.black,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 40),

              // Texte animé
              SlideTransition(
                position: _textSlideAnimation,
                child: FadeTransition(
                  opacity: _textFadeAnimation,
                  child: Column(
                    children: [
                      Text(
                        'HCP-DESIGN',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[900],
                          letterSpacing: 2,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Générateur de Factures',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.blue[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 30),

                      // Indicateur de chargement
                      SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.blue[700]!,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
    );
  }
}
