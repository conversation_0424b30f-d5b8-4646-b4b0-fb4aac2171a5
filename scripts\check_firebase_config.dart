import 'dart:io';
import 'dart:developer' as developer;

/// Script pour vérifier la configuration Firebase
class FirebaseConfigChecker {
  static Future<bool> checkWebConfig() async {
    try {
      final configFile = File('web/firebase-config.js');
      
      if (!await configFile.exists()) {
        developer.log('❌ Fichier firebase-config.js non trouvé');
        return false;
      }
      
      final content = await configFile.readAsString();
      
      // Vérifier si la configuration contient des valeurs par défaut
      if (content.contains('your-api-key-here') || 
          content.contains('your-project-id')) {
        developer.log('⚠️  Configuration Firebase non mise à jour');
        developer.log('   Veuillez modifier web/firebase-config.js avec votre vraie configuration');
        developer.log('   Consultez FIREBASE_SETUP.md pour les instructions');
        return false;
      }
      
      developer.log('✅ Configuration Firebase web trouvée');
      return true;
    } catch (e) {
      developer.log('❌ Erreur lors de la vérification de la configuration: $e');
      return false;
    }
  }
  
  static Future<bool> checkIndexHtml() async {
    try {
      final indexFile = File('web/index.html');
      
      if (!await indexFile.exists()) {
        developer.log('❌ Fichier index.html non trouvé');
        return false;
      }
      
      final content = await indexFile.readAsString();
      
      if (!content.contains('firebase-app-compat.js')) {
        developer.log('❌ Scripts Firebase non trouvés dans index.html');
        return false;
      }
      
      if (!content.contains('firebase-config.js')) {
        developer.log('❌ Référence à firebase-config.js non trouvée dans index.html');
        return false;
      }
      
      developer.log('✅ Configuration HTML Firebase correcte');
      return true;
    } catch (e) {
      developer.log('❌ Erreur lors de la vérification d\'index.html: $e');
      return false;
    }
  }
  
  static Future<void> checkAll() async {
    developer.log('=== Vérification de la configuration Firebase ===\n');
    
    final webConfigOk = await checkWebConfig();
    final indexHtmlOk = await checkIndexHtml();
    
    developer.log('\n=== Résumé ===');
    
    if (webConfigOk && indexHtmlOk) {
      developer.log('✅ Configuration Firebase complète');
      developer.log('   Vous pouvez maintenant lancer l\'application avec: flutter run');
    } else {
      developer.log('❌ Configuration Firebase incomplète');
      developer.log('   Veuillez suivre les instructions dans FIREBASE_SETUP.md');
      developer.log('\n📋 Actions requises:');
      
      if (!webConfigOk) {
        developer.log('   1. Configurer Firebase dans web/firebase-config.js');
      }
      
      if (!indexHtmlOk) {
        developer.log('   2. Vérifier les scripts Firebase dans web/index.html');
      }
      
      developer.log('\n📖 Documentation:');
      developer.log('   - FIREBASE_SETUP.md : Configuration Firebase');
      developer.log('   - ADMIN_SETUP.md : Identifiants administrateur');
    }
    
    developer.log('\n=== Fin de la vérification ===');
  }
}

/// Fonction principale
void main() async {
  await FirebaseConfigChecker.checkAll();
}