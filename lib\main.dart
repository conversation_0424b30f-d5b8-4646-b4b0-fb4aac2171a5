import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'pages/splash_screen.dart';
import 'services/notification_service.dart';
import 'services/sync_service.dart';
import 'scripts/init_admin.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeDateFormatting('fr_FR', null);
  
  // Lancer l'application immédiatement
  runApp(const GeneralHCPCRMApp());
  
  // Initialiser Firebase en arrière-plan
  _initializeFirebaseInBackground();
}

Future<void> _initializeFirebaseInBackground() async {
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('Firebase initialisé avec succès');
    
    // Initialiser l'administrateur par défaut
    await AdminInitializer.ensureAdminExists();
    debugPrint('Vérification de l\'administrateur terminée');
    
    // Initialiser le service de synchronisation en arrière-plan
    // Utiliser initializeInBackground au lieu de initialize pour ne pas bloquer
    SyncService.instance.initializeInBackground();
    
    // Initialiser le service de notifications
    await NotificationService().initialize();
    await NotificationService().startPeriodicNotifications();
  } catch (e) {
    debugPrint('Erreur lors de l\'initialisation en arrière-plan: $e');
  }
}

class GeneralHCPCRMApp extends StatelessWidget {
  const GeneralHCPCRMApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'General HCP CRM',
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('fr', 'FR'),
        Locale('en', 'US'),
      ],
      locale: const Locale('fr', 'FR'),
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.blue[900],
          foregroundColor: Colors.white,
          elevation: 2,
        ),
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
