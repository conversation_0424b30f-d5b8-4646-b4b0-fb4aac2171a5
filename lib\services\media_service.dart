import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
import 'permission_service.dart';

class MediaService {
  static final MediaService _instance = MediaService._internal();
  factory MediaService() => _instance;
  MediaService._internal();

  final ImagePicker _imagePicker = ImagePicker();
  final PermissionService _permissionService = PermissionService();
  final AudioRecorder _audioRecorder = AudioRecorder();

  /// Sélectionne une image depuis la galerie ou l'appareil photo
  Future<MediaFile?> pickImage({
    ImageSource source = ImageSource.gallery,
  }) async {
    try {
      // Demander les permissions avec le nouveau service
      bool hasPermission = false;
      if (source == ImageSource.camera) {
        hasPermission = await _permissionService.requestCameraPermission();
        if (!hasPermission) {
          throw Exception('Permission caméra refusée');
        }
      } else {
        hasPermission = await _permissionService.requestPhotosPermission();
        if (!hasPermission) {
          throw Exception('Permission photos refusée');
        }
      }

      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        final file = File(image.path);
        final bytes = await file.readAsBytes();
        final size = await file.length();

        return MediaFile(
          path: image.path,
          name: path.basename(image.path),
          size: size,
          mimeType: _getMimeType(image.path),
          type: MediaType.image,
          bytes: bytes,
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la sélection d\'image: $e');
    }
    return null;
  }

  /// Sélectionne une vidéo depuis la galerie ou l'appareil photo
  Future<MediaFile?> pickVideo({
    ImageSource source = ImageSource.gallery,
  }) async {
    try {
      // Demander les permissions avec le nouveau service
      bool hasPermission = false;
      if (source == ImageSource.camera) {
        hasPermission = await _permissionService.requestCameraPermission();
        if (!hasPermission) {
          throw Exception('Permission caméra refusée');
        }
      } else {
        hasPermission = await _permissionService.requestVideosPermission();
        if (!hasPermission) {
          throw Exception('Permission vidéos refusée');
        }
      }

      final XFile? video = await _imagePicker.pickVideo(
        source: source,
        maxDuration: const Duration(minutes: 5), // Limite WhatsApp
      );

      if (video != null) {
        final file = File(video.path);
        final bytes = await file.readAsBytes();
        final size = await file.length();

        // Vérifier la taille (limite WhatsApp: 16MB pour vidéo)
        if (size > 16 * 1024 * 1024) {
          throw Exception('La vidéo est trop volumineuse (max 16MB)');
        }

        return MediaFile(
          path: video.path,
          name: path.basename(video.path),
          size: size,
          mimeType: _getMimeType(video.path),
          type: MediaType.video,
          bytes: bytes,
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la sélection de vidéo: $e');
    }
    return null;
  }

  /// Sélectionne un document
  Future<MediaFile?> pickDocument() async {
    try {
      final hasPermission = await _permissionService.requestStoragePermission();
      if (!hasPermission) {
        throw Exception('Permission stockage refusée');
      }

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'xls',
          'xlsx',
          'ppt',
          'pptx',
          'txt',
        ],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          final fileObj = File(filePath);
          final bytes = await fileObj.readAsBytes();
          final size = file.size;

          // Vérifier la taille (limite WhatsApp: 100MB pour documents)
          if (size > 100 * 1024 * 1024) {
            throw Exception('Le document est trop volumineux (max 100MB)');
          }

          return MediaFile(
            path: filePath,
            name: file.name,
            size: size,
            mimeType: _getMimeType(filePath),
            type: MediaType.document,
            bytes: bytes,
          );
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la sélection de document: $e');
    }
    return null;
  }

  /// Enregistre un audio
  Future<MediaFile?> recordAudio() async {
    try {
      final hasPermission =
          await _permissionService.requestMicrophonePermission();
      if (!hasPermission) {
        throw Exception('Permission microphone refusée');
      }

      // Vérifier si l'enregistrement est supporté
      if (!await _audioRecorder.hasPermission()) {
        throw Exception('Permission microphone non accordée');
      }

      // Obtenir le répertoire temporaire
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${tempDir.path}/audio_$timestamp.m4a';

      // Configuration de l'enregistrement
      const config = RecordConfig(
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        sampleRate: 44100,
      );

      // Démarrer l'enregistrement
      await _audioRecorder.start(config, path: filePath);

      // Attendre que l'utilisateur arrête l'enregistrement
      // Note: Dans une vraie implémentation, vous devriez avoir une interface
      // pour contrôler l'enregistrement (start/stop)
      await Future.delayed(const Duration(seconds: 10)); // Exemple: 10 secondes

      // Arrêter l'enregistrement
      final recordedPath = await _audioRecorder.stop();

      if (recordedPath != null) {
        final file = File(recordedPath);
        if (await file.exists()) {
          final bytes = await file.readAsBytes();
          final size = await file.length();
          final fileName = path.basename(recordedPath);

          return MediaFile(
            path: recordedPath,
            name: fileName,
            size: size,
            mimeType: 'audio/mp4',
            type: MediaType.audio,
            bytes: bytes,
          );
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'enregistrement audio: $e');
    }
    return null;
  }

  /// Sélectionne un fichier audio
  Future<MediaFile?> pickAudio() async {
    try {
      final hasPermission = await _permissionService.requestAudioPermission();
      if (!hasPermission) {
        throw Exception('Permission audio refusée');
      }

      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          final fileObj = File(filePath);
          final bytes = await fileObj.readAsBytes();
          final size = file.size;

          // Vérifier la taille (limite WhatsApp: 16MB pour audio)
          if (size > 16 * 1024 * 1024) {
            throw Exception('Le fichier audio est trop volumineux (max 16MB)');
          }

          return MediaFile(
            path: filePath,
            name: file.name,
            size: size,
            mimeType: _getMimeType(filePath),
            type: MediaType.audio,
            bytes: bytes,
          );
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la sélection d\'audio: $e');
    }
    return null;
  }

  /// Sauvegarde un fichier média dans le dossier de l'application
  Future<String?> saveMediaFile(MediaFile mediaFile) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final mediaDir = Directory('${appDir.path}/media');

      if (!await mediaDir.exists()) {
        await mediaDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = '${timestamp}_${mediaFile.name}';
      final filePath = '${mediaDir.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(mediaFile.bytes);

      return filePath;
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde: $e');
      return null;
    }
  }

  /// Obtient le type MIME d'un fichier
  String _getMimeType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();

    switch (extension) {
      // Images
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';

      // Vidéos
      case '.mp4':
        return 'video/mp4';
      case '.avi':
        return 'video/avi';
      case '.mov':
        return 'video/quicktime';
      case '.mkv':
        return 'video/x-matroska';

      // Audio
      case '.mp3':
        return 'audio/mpeg';
      case '.wav':
        return 'audio/wav';
      case '.aac':
        return 'audio/aac';
      case '.ogg':
        return 'audio/ogg';
      case '.m4a':
        return 'audio/mp4';

      // Documents
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case '.txt':
        return 'text/plain';

      default:
        return 'application/octet-stream';
    }
  }

  /// Formate la taille d'un fichier
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Obtient l'icône pour un type de fichier
  IconData getFileIcon(String mimeType) {
    if (mimeType.startsWith('image/')) return Icons.image;
    if (mimeType.startsWith('video/')) return Icons.videocam;
    if (mimeType.startsWith('audio/')) return Icons.audiotrack;
    if (mimeType == 'application/pdf') return Icons.picture_as_pdf;
    if (mimeType.contains('word')) return Icons.description;
    if (mimeType.contains('excel') || mimeType.contains('spreadsheet')) {
      return Icons.table_chart;
    }
    if (mimeType.contains('powerpoint') || mimeType.contains('presentation')) {
      return Icons.slideshow;
    }
    if (mimeType == 'text/plain') return Icons.text_snippet;
    return Icons.insert_drive_file;
  }
}

enum MediaType { image, video, audio, document }

class MediaFile {
  final String path;
  final String name;
  final int size;
  final String mimeType;
  final MediaType type;
  final Uint8List bytes;

  MediaFile({
    required this.path,
    required this.name,
    required this.size,
    required this.mimeType,
    required this.type,
    required this.bytes,
  });
}
