import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/invoice.dart';

class InvoiceService {
  static const String _invoicesKey = 'hcp_invoices';
  final Uuid _uuid = const Uuid();

  // Sauvegarder toutes les factures
  static Future<void> saveInvoices(List<Invoice> invoices) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonList = invoices.map((invoice) => invoice.toJson()).toList();
    await prefs.setString(_invoicesKey, jsonEncode(jsonList));
  }

  // Charger toutes les factures
  static Future<List<Invoice>> loadInvoices() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_invoicesKey);
    
    if (jsonString == null) {
      return [];
    }

    try {
      final jsonList = jsonDecode(jsonString) as List;
      return jsonList.map((json) => Invoice.fromJson(json)).toList();
    } catch (e) {
      // En cas d'erreur de parsing, retourner une liste vide
      return [];
    }
  }

  // Ajouter une nouvelle facture
  Future<Invoice> addInvoice(Invoice invoice) async {
    final invoices = await InvoiceService.loadInvoices();
    final newInvoice = invoice.copyWith(id: _uuid.v4());
    invoices.add(newInvoice);
    await InvoiceService.saveInvoices(invoices);
    return newInvoice;
  }

  // Mettre à jour une facture existante
  Future<void> updateInvoice(Invoice updatedInvoice) async {
    final invoices = await InvoiceService.loadInvoices();
    final index = invoices.indexWhere((invoice) => invoice.id == updatedInvoice.id);
    
    if (index != -1) {
      invoices[index] = updatedInvoice;
      await InvoiceService.saveInvoices(invoices);
    }
  }

  // Supprimer une facture
  Future<void> deleteInvoice(String invoiceId) async {
    final invoices = await InvoiceService.loadInvoices();
    invoices.removeWhere((invoice) => invoice.id == invoiceId);
    await InvoiceService.saveInvoices(invoices);
  }

  // Obtenir une facture par ID
  Future<Invoice?> getInvoiceById(String id) async {
    final invoices = await InvoiceService.loadInvoices();
    try {
      return invoices.firstWhere((invoice) => invoice.id == id);
    } catch (e) {
      return null;
    }
  }

  // Filtrer les factures par statut
  Future<List<Invoice>> getInvoicesByStatus(InvoiceStatus status) async {
    final invoices = await InvoiceService.loadInvoices();
    return invoices.where((invoice) => invoice.status == status).toList();
  }

  // Rechercher des factures par nom de client
  Future<List<Invoice>> searchInvoicesByClientName(String query) async {
    final invoices = await InvoiceService.loadInvoices();
    final lowerQuery = query.toLowerCase();
    return invoices.where((invoice) => 
      invoice.clientName.toLowerCase().contains(lowerQuery)
    ).toList();
  }

  // Obtenir les statistiques des factures
  Future<Map<String, dynamic>> getInvoiceStats() async {
    final invoices = await InvoiceService.loadInvoices();
    
    double totalRevenue = 0;
    int paidCount = 0;
    int pendingCount = 0;
    int cancelledCount = 0;
    
    // Pour les communes les plus sollicitées
    Map<String, int> deliveryLocationCounts = {};
    final DateTime now = DateTime.now();
    final DateTime firstDayOfMonth = DateTime(now.year, now.month, 1);

    for (final invoice in invoices) {
      // Compter les livraisons par commune pour le mois en cours
      if (invoice.createdAt.isAfter(firstDayOfMonth)) {
        deliveryLocationCounts[invoice.deliveryLocation] = 
            (deliveryLocationCounts[invoice.deliveryLocation] ?? 0) + 1;
      }
      
      switch (invoice.status) {
        case InvoiceStatus.payee:
          // CA = prix des articles - remises (sans frais de livraison)
          double subtotal = InvoiceService().calculateSubtotal(invoice.items);
          totalRevenue += subtotal;
          paidCount++;
          break;
        case InvoiceStatus.enAttente:
          pendingCount++;
          break;
        case InvoiceStatus.annulee:
          cancelledCount++;
          break;
      }
    }
    
    // Trier les communes par nombre de livraisons (décroissant)
    var sortedLocations = deliveryLocationCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // Prendre les 4 premières communes
    var topLocations = sortedLocations.take(4).toList();

    return {
      'totalInvoices': invoices.length,
      'totalRevenue': totalRevenue,
      'paidCount': paidCount,
      'pendingCount': pendingCount,
      'cancelledCount': cancelledCount,
      'topDeliveryLocations': topLocations.map((e) => {
        'location': e.key,
        'count': e.value,
      }).toList(),
    };
  }

  // Générer un ID unique pour les articles
  String generateItemId() {
    return _uuid.v4();
  }

  // Calculer le sous-total des articles
  double calculateSubtotal(List<InvoiceItem> items) {
    return items.fold(0, (sum, item) => sum + item.total);
  }

  // Calculer le total final
  double calculateTotal({
    required double subtotal,
    required double deliveryPrice,
    required double discountAmount, // Ajout du paramètre de remise
    required double advance,
  }) {
    return subtotal + deliveryPrice - discountAmount - advance;
  }
}