// Configuration Firebase pour le web
// IMPORTANT: Remplacez ces valeurs par votre vraie configuration Firebase
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  databaseURL: "https://your-project-id-default-rtdb.firebaseio.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};

// Configuration pour le développement local (optionnel)
if (window.location.hostname === 'localhost') {
  console.log('Mode développement détecté');
  // Vous pouvez utiliser l'émulateur Firebase ici si nécessaire
}

// Initialiser Firebase
if (typeof firebase !== 'undefined') {
  firebase.initializeApp(firebaseConfig);
  console.log('Firebase initialisé pour le web');
} else {
  console.warn('Firebase SDK non chargé');
}