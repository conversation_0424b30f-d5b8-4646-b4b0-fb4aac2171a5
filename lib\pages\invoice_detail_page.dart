import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';
import '../services/pdf_service.dart';
import '../widgets/password_dialog.dart';
import 'pdf_preview_page.dart';
import 'edit_invoice_page.dart';

class InvoiceDetailPage extends StatefulWidget {
  final String invoiceId;

  const InvoiceDetailPage({super.key, required this.invoiceId});

  @override
  State<InvoiceDetailPage> createState() => _InvoiceDetailPageState();
}

class _InvoiceDetailPageState extends State<InvoiceDetailPage> {
  Invoice? _invoice;
  bool _isLoading = true;
  bool _isGeneratingPDF = false;
  bool _isGeneratingMiniPDF = false;
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy à HH:mm');
  final InvoiceService _invoiceService = InvoiceService();

  @override
  void initState() {
    super.initState();
    _loadInvoice();
  }

  Future<void> _loadInvoice() async {
    try {
      final invoice = await _invoiceService.getInvoiceById(widget.invoiceId);
      setState(() {
        _invoice = invoice;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  Future<void> _updateStatus(InvoiceStatus newStatus) async {
    if (_invoice == null) return;

    try {
      final updatedInvoice = _invoice!.copyWith(status: newStatus);
      await _invoiceService.updateInvoice(updatedInvoice);
      setState(() {
        _invoice = updatedInvoice;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Statut mis à jour avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la mise à jour: $e')),
        );
      }
    }
  }

  Future<void> _generatePDF() async {
    if (_invoice == null) return;

    setState(() => _isGeneratingPDF = true);

    try {
      // Générer le PDF pour prévisualisation
      final pdfDocument = await PDFService.generateInvoicePDF(_invoice!);

      if (mounted) {
        // Naviguer vers la page de prévisualisation
        final prefix =
            _invoice!.type == InvoiceType.proforma ? 'Proforma' : 'Facture';
        final fileName =
            '${prefix}_${_invoice!.invoiceNumber}_${_invoice!.clientName.replaceAll(' ', '_')}.pdf';

        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => PDFPreviewPage(
                  pdfDocument: pdfDocument,
                  title: 'Prévisualisation $prefix',
                  fileName: fileName,
                ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la génération PDF: $e')),
        );
      }
    } finally {
      setState(() => _isGeneratingPDF = false);
    }
  }

  Future<void> _generateMiniPDF() async {
    if (_invoice == null) return;

    setState(() => _isGeneratingMiniPDF = true);

    try {
      // Calculer le montant dû (total - avance)
      final amountDue = _invoice!.total - _invoice!.advance;

      // Générer la mini facture pour prévisualisation
      final pdfDocument = await PDFService.generateMiniInvoice(
        nomClient: _invoice!.clientName,
        numeroClient: _invoice!.clientNumber,
        lieuLivraison: _invoice!.deliveryLocation,
        resteAPayer: amountDue,
      );

      if (mounted) {
        // Naviguer vers la page de prévisualisation
        final fileName =
            'mini_facture_${_invoice!.clientNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf';

        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => PDFPreviewPage(
                  pdfDocument: pdfDocument,
                  title: 'Prévisualisation Mini Facture',
                  fileName: fileName,
                ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la génération mini PDF: $e')),
        );
      }
    } finally {
      setState(() => _isGeneratingMiniPDF = false);
    }
  }

  Future<void> _deleteInvoice() async {
    if (_invoice == null) return;

    // Vérification du mot de passe avant suppression
    final passwordConfirmed = await PasswordDialog.show(
      context: context,
      title: 'Authentification requise',
      message:
          'Pour supprimer la facture ${_invoice!.invoiceNumber}, veuillez saisir le mot de passe administrateur.',
    );

    if (!passwordConfirmed) return;

    // Confirmation finale de suppression
    if (!mounted) return;
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer la facture ${_invoice!.invoiceNumber} ?\n\nCette action est irréversible.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        await _invoiceService.deleteInvoice(widget.invoiceId);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Facture supprimée avec succès')),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la suppression: $e')),
          );
        }
      }
    }
  }

  Future<void> _editInvoice() async {
    if (_invoice == null) return;

    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => EditInvoicePage(invoice: _invoice!),
      ),
    );

    if (result == true) {
      _loadInvoice(); // Recharger la facture après modification
    }
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.enAttente:
        return Colors.orange;

      case InvoiceStatus.payee:
        return Colors.teal;
      case InvoiceStatus.annulee:
        return Colors.red;
    }
  }

  String _getStatusText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.enAttente:
        return 'En attente';

      case InvoiceStatus.payee:
        return 'Payée';
      case InvoiceStatus.annulee:
        return 'Annulée';
    }
  }

  Widget _buildStatusChip() {
    if (_invoice == null) return const SizedBox();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor(_invoice!.status),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        _getStatusText(_invoice!.status),
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    if (_invoice?.items.isEmpty ?? true) {
      return const Text('Aucun article');
    }

    return Column(
      children:
          _invoice!.items.asMap().entries.map((entry) {
            final item = entry.value;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          item.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      if (!item.isCustom && item.categoryName != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Catégorie',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.blue[800],
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Prix unitaire: ${_currencyFormat.format(item.price)} FCFA',
                        style: const TextStyle(color: Colors.grey),
                      ),
                      Text(
                        'Qté: ${item.quantity}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'Total: ${_currencyFormat.format(item.total)} FCFA',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Widget _buildTotalSection() {
    if (_invoice == null) return const SizedBox();

    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'RÉCAPITULATIF FINANCIER',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 12),
            _buildTotalRow(
              'Sous-total:',
              '${_currencyFormat.format(_invoice!.subtotal)} FCFA',
            ),
            _buildTotalRow(
              'Livraison:',
              '${_currencyFormat.format(_invoice!.deliveryPrice)} FCFA',
            ),
            if (_invoice!.advance > 0)
              _buildTotalRow(
                'Avance:',
                '-${_currencyFormat.format(_invoice!.advance)} FCFA',
              ),
            const Divider(thickness: 2),
            _buildTotalRow(
              'TOTAL:',
              '${_currencyFormat.format(_invoice!.total)} FCFA',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.blue : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    if (_invoice == null) return const SizedBox();

    return Column(
      children: [
        // Boutons de changement de statut
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'CHANGER LE STATUT',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      InvoiceStatus.values.map((status) {
                        final isCurrentStatus = status == _invoice!.status;
                        return ElevatedButton(
                          onPressed:
                              isCurrentStatus
                                  ? null
                                  : () => _updateStatus(status),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                isCurrentStatus
                                    ? Colors.grey[300]
                                    : _getStatusColor(status),
                            foregroundColor:
                                isCurrentStatus ? Colors.grey : Colors.white,
                          ),
                          child: Text(_getStatusText(status)),
                        );
                      }).toList(),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Boutons d'action principaux
        Column(
          children: [
            // Première ligne: PDF A4 et Mini PDF
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGeneratingPDF ? null : _generatePDF,
                    icon:
                        _isGeneratingPDF
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Icon(Icons.picture_as_pdf),
                    label: Text(_isGeneratingPDF ? 'Génération...' : 'PDF A4'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGeneratingMiniPDF ? null : _generateMiniPDF,
                    icon:
                        _isGeneratingMiniPDF
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Icon(Icons.receipt),
                    label: Text(
                      _isGeneratingMiniPDF ? 'Génération...' : 'Mini PDF 5x3"',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Deuxième ligne: Modifier
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _editInvoice,
                icon: const Icon(Icons.edit),
                label: const Text('Modifier la facture'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[900],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _deleteInvoice,
            icon: const Icon(Icons.delete),
            label: const Text('Supprimer la facture'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_invoice?.invoiceNumber ?? 'Chargement...'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        actions: [
          if (_invoice != null) _buildStatusChip(),
          const SizedBox(width: 16),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _invoice == null
              ? const Center(
                child: Text(
                  'Facture introuvable',
                  style: TextStyle(fontSize: 18),
                ),
              )
              : ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Informations générales
                  _buildInfoCard('INFORMATIONS GÉNÉRALES', [
                    _buildInfoRow(
                      'N° Facture:',
                      _invoice!.invoiceNumber,
                      isBold: true,
                    ),
                    _buildInfoRow(
                      'Date création:',
                      _dateFormat.format(_invoice!.createdAt),
                    ),
                    _buildInfoRow('Statut:', _getStatusText(_invoice!.status)),
                  ]),

                  // Informations client
                  _buildInfoCard('INFORMATIONS CLIENT', [
                    _buildInfoRow('Nom:', _invoice!.clientName),
                    _buildInfoRow('Téléphone:', _invoice!.clientNumber),
                    _buildInfoRow('Commande:', _invoice!.products),
                  ]),

                  // Articles
                  _buildInfoCard('ARTICLES (${_invoice!.items.length})', [
                    _buildItemsList(),
                  ]),

                  // Livraison
                  _buildInfoCard('LIVRAISON', [
                    _buildInfoRow('Zone:', _invoice!.deliveryLocation),
                    _buildInfoRow(
                      'Prix livraison:',
                      '${_currencyFormat.format(_invoice!.deliveryPrice)} FCFA',
                    ),
                  ]),

                  // Notes (si présentes)
                  if (_invoice!.notes?.isNotEmpty == true)
                    _buildInfoCard('NOTES', [
                      Text(
                        _invoice!.notes!,
                        style: const TextStyle(fontStyle: FontStyle.italic),
                      ),
                    ]),

                  // Récapitulatif financier
                  _buildTotalSection(),

                  const SizedBox(height: 24),

                  // Boutons d'action
                  _buildActionButtons(),
                ],
              ),
    );
  }
}
