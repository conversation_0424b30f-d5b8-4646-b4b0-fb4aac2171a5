enum MessageType { text, image, document, audio, video, location, contact }

enum MessageStatus { sent, delivered, read, failed }

enum ChatStatus { active, archived, blocked, pending }

enum CustomerIntention {
  achat('ACHAT'),
  information('INFORMATION'),
  support('SUPPORT'),
  unknown('UNKNOWN');

  const CustomerIntention(this.value);
  final String value;
}

enum SupportTicketType {
  orderTracking('ORDER_TRACKING'),
  returnRefund('RETURN_REFUND'),
  productIssue('PRODUCT_ISSUE'),
  technicalSupport('TECHNICAL_SUPPORT'),
  complaint('COMPLAINT'),
  generalInquiry('GENERAL_INQUIRY');

  const SupportTicketType(this.value);
  final String value;
}

class WhatsAppMessage {
  final String id;
  final String chatId;
  final String content;
  final MessageType type;
  final MessageStatus status;
  final DateTime timestamp;
  final bool isFromCustomer;
  final String? mediaUrl;
  final String? mediaCaption;
  final String? fileName;
  final int? fileSize;
  final String? mimeType;
  final Map<String, dynamic>? metadata;
  final CustomerIntention? detectedIntention;
  final bool isAiGenerated;
  final String? aiModel;

  WhatsAppMessage({
    required this.id,
    required this.chatId,
    required this.content,
    required this.type,
    required this.status,
    required this.timestamp,
    required this.isFromCustomer,
    this.mediaUrl,
    this.mediaCaption,
    this.fileName,
    this.fileSize,
    this.mimeType,
    this.metadata,
    this.detectedIntention,
    this.isAiGenerated = false,
    this.aiModel,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'chatId': chatId,
    'content': content,
    'type': type.name,
    'status': status.name,
    'timestamp': timestamp.toIso8601String(),
    'isFromCustomer': isFromCustomer,
    'mediaUrl': mediaUrl,
    'mediaCaption': mediaCaption,
    'fileName': fileName,
    'fileSize': fileSize,
    'mimeType': mimeType,
    'metadata': metadata,
    'detectedIntention': detectedIntention?.value,
    'isAiGenerated': isAiGenerated,
    'aiModel': aiModel,
  };

  factory WhatsAppMessage.fromJson(Map<String, dynamic> json) =>
      WhatsAppMessage(
        id: json['id'],
        chatId: json['chatId'],
        content: json['content'],
        type: MessageType.values.firstWhere((e) => e.name == json['type']),
        status: MessageStatus.values.firstWhere(
          (e) => e.name == json['status'],
        ),
        timestamp: DateTime.parse(json['timestamp']),
        isFromCustomer: json['isFromCustomer'],
        mediaUrl: json['mediaUrl'],
        mediaCaption: json['mediaCaption'],
        fileName: json['fileName'],
        fileSize: json['fileSize'],
        mimeType: json['mimeType'],
        metadata: json['metadata'],
        detectedIntention:
            json['detectedIntention'] != null
                ? CustomerIntention.values.firstWhere(
                  (e) => e.value == json['detectedIntention'],
                )
                : null,
        isAiGenerated: json['isAiGenerated'] ?? false,
        aiModel: json['aiModel'],
      );

  WhatsAppMessage copyWith({
    String? id,
    String? chatId,
    String? content,
    MessageType? type,
    MessageStatus? status,
    DateTime? timestamp,
    bool? isFromCustomer,
    String? mediaUrl,
    String? mediaCaption,
    String? fileName,
    int? fileSize,
    String? mimeType,
    Map<String, dynamic>? metadata,
    CustomerIntention? detectedIntention,
    bool? isAiGenerated,
    String? aiModel,
  }) => WhatsAppMessage(
    id: id ?? this.id,
    chatId: chatId ?? this.chatId,
    content: content ?? this.content,
    type: type ?? this.type,
    status: status ?? this.status,
    timestamp: timestamp ?? this.timestamp,
    isFromCustomer: isFromCustomer ?? this.isFromCustomer,
    mediaUrl: mediaUrl ?? this.mediaUrl,
    mediaCaption: mediaCaption ?? this.mediaCaption,
    fileName: fileName ?? this.fileName,
    fileSize: fileSize ?? this.fileSize,
    mimeType: mimeType ?? this.mimeType,
    metadata: metadata ?? this.metadata,
    detectedIntention: detectedIntention ?? this.detectedIntention,
    isAiGenerated: isAiGenerated ?? this.isAiGenerated,
    aiModel: aiModel ?? this.aiModel,
  );
}

class WhatsAppChat {
  final String id;
  final String customerName;
  final String customerPhone;
  final String? customerAvatar;
  final ChatStatus status;
  final DateTime lastMessageTime;
  final String lastMessage;
  final int unreadCount;
  final List<WhatsAppMessage> messages;
  final CustomerProfile? customerProfile;
  final Map<String, dynamic>? metadata;
  final bool isAiEnabled;
  final String? assignedAgent;

  WhatsAppChat({
    required this.id,
    required this.customerName,
    required this.customerPhone,
    this.customerAvatar,
    required this.status,
    required this.lastMessageTime,
    required this.lastMessage,
    required this.unreadCount,
    required this.messages,
    this.customerProfile,
    this.metadata,
    this.isAiEnabled = true,
    this.assignedAgent,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'customerName': customerName,
    'customerPhone': customerPhone,
    'customerAvatar': customerAvatar,
    'status': status.name,
    'lastMessageTime': lastMessageTime.toIso8601String(),
    'lastMessage': lastMessage,
    'unreadCount': unreadCount,
    'messages': messages.map((m) => m.toJson()).toList(),
    'customerProfile': customerProfile?.toJson(),
    'metadata': metadata,
    'isAiEnabled': isAiEnabled,
    'assignedAgent': assignedAgent,
  };

  factory WhatsAppChat.fromJson(Map<String, dynamic> json) => WhatsAppChat(
    id: json['id'],
    customerName: json['customerName'],
    customerPhone: json['customerPhone'],
    customerAvatar: json['customerAvatar'],
    status: ChatStatus.values.firstWhere((e) => e.name == json['status']),
    lastMessageTime: DateTime.parse(json['lastMessageTime']),
    lastMessage: json['lastMessage'],
    unreadCount: json['unreadCount'],
    messages:
        (json['messages'] as List)
            .map((m) => WhatsAppMessage.fromJson(m))
            .toList(),
    customerProfile:
        json['customerProfile'] != null
            ? CustomerProfile.fromJson(json['customerProfile'])
            : null,
    metadata: json['metadata'],
    isAiEnabled: json['isAiEnabled'] ?? true,
    assignedAgent: json['assignedAgent'],
  );

  WhatsAppChat copyWith({
    String? id,
    String? customerName,
    String? customerPhone,
    String? customerAvatar,
    ChatStatus? status,
    DateTime? lastMessageTime,
    String? lastMessage,
    int? unreadCount,
    List<WhatsAppMessage>? messages,
    CustomerProfile? customerProfile,
    Map<String, dynamic>? metadata,
    bool? isAiEnabled,
    String? assignedAgent,
  }) => WhatsAppChat(
    id: id ?? this.id,
    customerName: customerName ?? this.customerName,
    customerPhone: customerPhone ?? this.customerPhone,
    customerAvatar: customerAvatar ?? this.customerAvatar,
    status: status ?? this.status,
    lastMessageTime: lastMessageTime ?? this.lastMessageTime,
    lastMessage: lastMessage ?? this.lastMessage,
    unreadCount: unreadCount ?? this.unreadCount,
    messages: messages ?? this.messages,
    customerProfile: customerProfile ?? this.customerProfile,
    metadata: metadata ?? this.metadata,
    isAiEnabled: isAiEnabled ?? this.isAiEnabled,
    assignedAgent: assignedAgent ?? this.assignedAgent,
  );
}

class CustomerProfile {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String? address;
  final List<String> preferences;
  final List<String> purchaseHistory;
  final CustomerIntention? primaryIntention;
  final Map<String, dynamic> behaviorData;
  final DateTime createdAt;
  final DateTime updatedAt;

  CustomerProfile({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    this.address,
    required this.preferences,
    required this.purchaseHistory,
    this.primaryIntention,
    required this.behaviorData,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'phone': phone,
    'email': email,
    'address': address,
    'preferences': preferences,
    'purchaseHistory': purchaseHistory,
    'primaryIntention': primaryIntention?.value,
    'behaviorData': behaviorData,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory CustomerProfile.fromJson(Map<String, dynamic> json) =>
      CustomerProfile(
        id: json['id'],
        name: json['name'],
        phone: json['phone'],
        email: json['email'],
        address: json['address'],
        preferences: List<String>.from(json['preferences'] ?? []),
        purchaseHistory: List<String>.from(json['purchaseHistory'] ?? []),
        primaryIntention:
            json['primaryIntention'] != null
                ? CustomerIntention.values.firstWhere(
                  (e) => e.value == json['primaryIntention'],
                )
                : null,
        behaviorData: json['behaviorData'] ?? {},
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
      );
}

class SupportTicket {
  final String id;
  final String chatId;
  final String customerId;
  final SupportTicketType type;
  final String title;
  final String description;
  final String status;
  final String priority;
  final String? assignedAgent;
  final DateTime createdAt;
  final DateTime? resolvedAt;
  final List<String> tags;

  SupportTicket({
    required this.id,
    required this.chatId,
    required this.customerId,
    required this.type,
    required this.title,
    required this.description,
    required this.status,
    required this.priority,
    this.assignedAgent,
    required this.createdAt,
    this.resolvedAt,
    required this.tags,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'chatId': chatId,
    'customerId': customerId,
    'type': type.value,
    'title': title,
    'description': description,
    'status': status,
    'priority': priority,
    'assignedAgent': assignedAgent,
    'createdAt': createdAt.toIso8601String(),
    'resolvedAt': resolvedAt?.toIso8601String(),
    'tags': tags,
  };

  factory SupportTicket.fromJson(Map<String, dynamic> json) => SupportTicket(
    id: json['id'],
    chatId: json['chatId'],
    customerId: json['customerId'],
    type: SupportTicketType.values.firstWhere((e) => e.value == json['type']),
    title: json['title'],
    description: json['description'],
    status: json['status'],
    priority: json['priority'],
    assignedAgent: json['assignedAgent'],
    createdAt: DateTime.parse(json['createdAt']),
    resolvedAt:
        json['resolvedAt'] != null ? DateTime.parse(json['resolvedAt']) : null,
    tags: List<String>.from(json['tags'] ?? []),
  );
}
