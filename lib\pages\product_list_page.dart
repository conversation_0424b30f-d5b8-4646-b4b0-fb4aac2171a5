import 'package:flutter/material.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/models/category.dart';
import 'package:general_hcp_crm/services/inventory_service.dart';
import 'add_edit_product_page.dart';
import 'import_export_page.dart';

class ProductListPage extends StatefulWidget {
  const ProductListPage({super.key});

  @override
  State<ProductListPage> createState() => _ProductListPageState();
}

class _ProductListPageState extends State<ProductListPage> {
  final InventoryService _inventoryService = InventoryService.instance;
  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  List<Category> _categories = [];
  Category? _selectedCategory;
  String _searchTerm = '';
  bool _isLoading = true;
  bool _isGridView = false; // Pour basculer entre vue liste et grille

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }
    try {
      _products = await _inventoryService.getProducts();
      _categories = await _inventoryService.getCategories();
      _applyFilters();
    } catch (e) {
      // Gérer l'erreur, par exemple afficher un message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur de chargement des données: ${e.toString()}'),
          ),
        );
      }
    }
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    _filteredProducts =
        _products.where((product) {
          final matchesCategory =
              _selectedCategory == null ||
              product.categoryId == _selectedCategory!.id;
          final matchesSearchTerm =
              _searchTerm.isEmpty ||
              product.name.toLowerCase().contains(_searchTerm.toLowerCase());
          return matchesCategory && matchesSearchTerm;
        }).toList();
    if (mounted) {
      setState(() {});
    }
  }

  void _onSearchChanged(String term) {
    _searchTerm = term;
    _applyFilters();
  }

  void _onCategoryChanged(Category? category) {
    _selectedCategory = category;
    _applyFilters();
  }

  void _toggleView() {
    if (mounted) {
      setState(() {
        _isGridView = !_isGridView;
      });
    }
  }

  void _navigateToAddProduct() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEditProductPage()),
    );
    if (result == true && mounted) {
      _loadData(); // Recharger les données si un produit a été ajouté/modifié
    }
  }

  void _navigateToImportExport() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ImportExportPage()),
    );
    if (result == true && mounted) {
      _loadData(); // Recharger les données si des produits ont été importés
    }
  }

  void _navigateToEditProduct(Product product) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditProductPage(product: product),
      ),
    );
    if (result == true && mounted) {
      _loadData(); // Recharger les données si un produit a été ajouté/modifié
    }
  }

  Future<void> _deleteProduct(Product product) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text(
            'Voulez-vous vraiment supprimer le produit "${product.name}" ?',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Annuler'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      try {
        await _inventoryService.deleteProduct(product.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Produit "${product.name}" supprimé.')),
          );
        }
        if (mounted) {
          _loadData();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la suppression: ${e.toString()}'),
            ),
          );
        }
      }
    }
  }

  void _showAddCategoryDialog() {
    final nameController = TextEditingController();
    final priceController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Ajouter une catégorie'),
            content: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Nom de la catégorie',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Veuillez entrer un nom de catégorie';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: priceController,
                    decoration: const InputDecoration(
                      labelText: 'Prix par défaut (optionnel)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.euro),
                      suffixText: '€',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final price = double.tryParse(value);
                        if (price == null || price < 0) {
                          return 'Veuillez entrer un prix valide';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    final navigator = Navigator.of(context);
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    final defaultPrice =
                        priceController.text.isNotEmpty
                            ? double.tryParse(priceController.text)
                            : null;

                    final newCategory = Category(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: nameController.text.trim(),
                      defaultPrice: defaultPrice,
                    );

                    try {
                      await _inventoryService.addCategory(newCategory);
                      if (mounted) {
                        navigator.pop();
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              'Catégorie "${newCategory.name}" ajoutée.',
                            ),
                          ),
                        );
                        if (mounted) {
                          _loadData(); // Recharger les données pour mettre à jour la liste des catégories
                        }
                      }
                    } catch (e) {
                      if (mounted) {
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              'Erreur lors de l\'ajout: ${e.toString()}',
                            ),
                          ),
                        );
                      }
                    }
                  }
                },
                child: const Text('Ajouter'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HCP-DESIGN - Inventaire'),
        backgroundColor: Colors.blue[900], // Cohérent avec le dashboard
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.import_export),
            onPressed: _navigateToImportExport,
            tooltip: 'Import / Export',
          ),
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: _toggleView,
            tooltip: _isGridView ? 'Vue Liste' : 'Vue Grille',
          ),
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: _showAddCategoryDialog,
            tooltip: 'Ajouter une catégorie',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  _buildFilterBar(),
                  Expanded(
                    child:
                        _filteredProducts.isEmpty
                            ? _buildEmptyState()
                            : _isGridView
                            ? _buildProductGridView()
                            : _buildProductListView(),
                  ),
                ],
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddProduct,
        tooltip: 'Ajouter un produit',
        backgroundColor: Colors.blue[900], // Cohérent avec l'AppBar
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                _searchTerm.isNotEmpty || _selectedCategory != null
                    ? Icons.search_off
                    : Icons.inventory_2_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _searchTerm.isNotEmpty || _selectedCategory != null
                  ? 'Aucun produit trouvé'
                  : 'Aucun produit disponible',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchTerm.isNotEmpty || _selectedCategory != null
                  ? 'Essayez de modifier vos critères de recherche'
                  : 'Commencez par ajouter votre premier produit',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (_searchTerm.isEmpty && _selectedCategory == null)
              ElevatedButton.icon(
                onPressed: _navigateToAddProduct,
                icon: const Icon(Icons.add),
                label: const Text('Ajouter un produit'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Column(
        children: [
          // Search field
          TextField(
            onChanged: _onSearchChanged,
            decoration: InputDecoration(
              hintText: 'Rechercher un produit...',
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue[600]!),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Category filter
          Row(
            children: [
              Icon(Icons.filter_list, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: DropdownButton<Category?>(
                    value: _selectedCategory,
                    hint: const Text('Toutes les catégories'),
                    isExpanded: true,
                    underline: const SizedBox(),
                    items: [
                      const DropdownMenuItem<Category?>(
                        value: null,
                        child: Text('Toutes les catégories'),
                      ),
                      ..._categories.map((Category category) {
                        return DropdownMenuItem<Category?>(
                          value: category,
                          child: Text(category.name),
                        );
                      }),
                    ],
                    onChanged: _onCategoryChanged,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductListView() {
    return ListView.builder(
      itemCount: _filteredProducts.length,
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        final category = _categories.firstWhere(
          (cat) => cat.id == product.categoryId,
          orElse: () => Category(id: '', name: 'Non catégorisé'),
        );
        
        // Déterminer la couleur basée sur le stock
        Color stockColor;
        if (product.quantity > 5) {
          stockColor = Colors.green;
        } else if (product.quantity == 5) {
          stockColor = Colors.orange;
        } else {
          stockColor = Colors.red;
        }
        
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: ListTile(
            leading:
                product.imageUrl != null && product.imageUrl!.isNotEmpty
                    ? Image.network(
                      product.imageUrl!,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      errorBuilder:
                          (c, o, s) =>
                              const Icon(Icons.image_not_supported, size: 50),
                    )
                    : const Icon(Icons.inventory_2_outlined, size: 50),
            title: Text(product.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Prix: ${product.price} € - Quantité: ${product.quantity}',
                ),
                const SizedBox(height: 2),
                Text(
                  category.name,
                  style: TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                    color: stockColor,
                  ),
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  _navigateToEditProduct(product);
                } else if (value == 'delete') {
                  _deleteProduct(product);
                }
              },
              itemBuilder:
                  (BuildContext context) => <PopupMenuEntry<String>>[
                    const PopupMenuItem<String>(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('Modifier'),
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete, color: Colors.red),
                        title: Text(
                          'Supprimer',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    ),
                  ],
            ),
            onTap: () => _navigateToEditProduct(product),
          ),
        );
      },
    );
  }

  Widget _buildProductGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // ou 3, selon la taille de l'écran
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 0.75, // Ajusté pour accommoder la catégorie
      ),
      itemCount: _filteredProducts.length,
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        final category = _categories.firstWhere(
          (cat) => cat.id == product.categoryId,
          orElse: () => Category(id: '', name: 'Non catégorisé'),
        );
        
        // Déterminer la couleur basée sur le stock
        Color stockColor;
        if (product.quantity > 5) {
          stockColor = Colors.green;
        } else if (product.quantity == 5) {
          stockColor = Colors.orange;
        } else {
          stockColor = Colors.red;
        }
        
        return Card(
          // onTap: () => _navigateToEditProduct(product),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child:
                    product.imageUrl != null && product.imageUrl!.isNotEmpty
                        ? Image.network(
                          product.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder:
                              (c, o, s) => const Icon(
                                Icons.image_not_supported,
                                size: 80,
                              ),
                        )
                        : const Icon(Icons.inventory_2_outlined, size: 80),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  product.name,
                  style: Theme.of(context).textTheme.titleMedium,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(
                  'Prix: ${product.price} €',
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0, 8.0, 4.0),
                child: Text(
                  'Stock: ${product.quantity}',
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(8.0, 0, 8.0, 8.0),
                child: Text(
                  category.name,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                    color: stockColor,
                  ),
                ),
              ),
              Align(
                alignment:
                    Alignment
                        .centerRight, // Changé pour mieux positionner le bouton
                child: IconButton(
                  icon: const Icon(Icons.edit_note),
                  tooltip: 'Modifier',
                  onPressed: () => _navigateToEditProduct(product),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Helper pour afficher le nom de la catégorie (si nécessaire ailleurs)
// Future<String> _getCategoryName(String categoryId, InventoryService service) async {
//   final category = await service.getCategoryById(categoryId);
//   return category?.name ?? 'N/A';
// }
