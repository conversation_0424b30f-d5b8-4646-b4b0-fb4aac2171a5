import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/whatsapp_chat.dart';
import '../services/whatsapp_service.dart';
import '../services/ai_service.dart';
import '../services/knowledge_service.dart';
import '../services/media_service.dart' as media_service;

class WhatsAppWebhookService {
  static final WhatsAppWebhookService _instance =
      WhatsAppWebhookService._internal();
  factory WhatsAppWebhookService() => _instance;
  WhatsAppWebhookService._internal();

  static const String _configKey = 'whatsapp_webhook_config';
  static const int _webhookPort = 8081;

  HttpServer? _webhookServer;
  bool _isServerRunning = false;

  // Configuration WhatsApp Business API
  String _accessToken = '';
  String _phoneNumberId = '';
  String _verifyToken = '';
  String _webhookUrl = '';
  bool _isConfigured = false;

  final WhatsAppService _whatsappService = WhatsAppService();
  final AIService _aiService = AIService();
  final KnowledgeService _knowledgeService = KnowledgeService();
  final media_service.MediaService _mediaService = media_service.MediaService();

  /// Initialise le service webhook
  Future<void> initialize() async {
    await _loadConfiguration();
    await _whatsappService.initialize();
    await _aiService.initialize();
    await _knowledgeService.initialize();
  }

  /// Démarre le serveur webhook
  Future<bool> startWebhookServer() async {
    try {
      _webhookServer = await HttpServer.bind(
        InternetAddress.anyIPv4,
        _webhookPort,
      );
      _isServerRunning = true;

      debugPrint('Serveur webhook WhatsApp démarré sur le port $_webhookPort');

      _webhookServer!.listen((HttpRequest request) async {
        await _handleWebhookRequest(request);
      });

      return true;
    } catch (e) {
      debugPrint('Erreur lors du démarrage du serveur webhook: $e');
      return false;
    }
  }

  /// Arrête le serveur webhook
  Future<void> stopWebhookServer() async {
    if (_webhookServer != null) {
      await _webhookServer!.close();
      _webhookServer = null;
      _isServerRunning = false;
      debugPrint('Serveur webhook WhatsApp arrêté');
    }
  }

  /// Gère les requêtes webhook entrantes
  Future<void> _handleWebhookRequest(HttpRequest request) async {
    // Ajouter les en-têtes CORS
    request.response.headers.add('Access-Control-Allow-Origin', '*');
    request.response.headers.add(
      'Access-Control-Allow-Methods',
      'GET, POST, OPTIONS',
    );
    request.response.headers.add(
      'Access-Control-Allow-Headers',
      'Content-Type',
    );

    try {
      if (request.method == 'GET') {
        await _handleVerification(request);
      } else if (request.method == 'POST') {
        await _handleIncomingMessage(request);
      } else if (request.method == 'OPTIONS') {
        request.response.statusCode = 200;
      } else {
        request.response.statusCode = 405;
        request.response.write('Method Not Allowed');
      }
    } catch (e) {
      debugPrint('Erreur lors du traitement de la requête webhook: $e');
      request.response.statusCode = 500;
      request.response.write('Internal Server Error');
    }

    await request.response.close();
  }

  /// Gère la vérification du webhook (challenge)
  Future<void> _handleVerification(HttpRequest request) async {
    final uri = request.uri;
    final mode = uri.queryParameters['hub.mode'];
    final token = uri.queryParameters['hub.verify_token'];
    final challenge = uri.queryParameters['hub.challenge'];

    if (mode == 'subscribe' && token == _verifyToken) {
      debugPrint('Webhook vérifié avec succès');
      request.response.statusCode = 200;
      request.response.write(challenge);
    } else {
      debugPrint('Échec de la vérification du webhook');
      request.response.statusCode = 403;
      request.response.write('Forbidden');
    }
  }

  /// Gère les messages entrants
  Future<void> _handleIncomingMessage(HttpRequest request) async {
    try {
      final body = await utf8.decoder.bind(request).join();
      final data = jsonDecode(body) as Map<String, dynamic>;

      debugPrint('Message WhatsApp reçu: $body');

      // Vérifier la structure du webhook WhatsApp
      if (data['object'] == 'whatsapp_business_account') {
        final entries = data['entry'] as List?;
        if (entries != null && entries.isNotEmpty) {
          for (final entry in entries) {
            await _processWebhookEntry(entry);
          }
        }
      }

      request.response.statusCode = 200;
      request.response.write('OK');
    } catch (e) {
      debugPrint('Erreur lors du traitement du message entrant: $e');
      request.response.statusCode = 400;
      request.response.write('Bad Request');
    }
  }

  /// Traite une entrée du webhook
  Future<void> _processWebhookEntry(Map<String, dynamic> entry) async {
    final changes = entry['changes'] as List?;
    if (changes == null || changes.isEmpty) return;

    for (final change in changes) {
      final value = change['value'] as Map<String, dynamic>?;
      if (value == null) continue;

      // Traiter les messages
      final messages = value['messages'] as List?;
      if (messages != null && messages.isNotEmpty) {
        for (final messageData in messages) {
          await _processIncomingMessage(messageData, value);
        }
      }

      // Traiter les statuts de messages
      final statuses = value['statuses'] as List?;
      if (statuses != null && statuses.isNotEmpty) {
        for (final statusData in statuses) {
          await _processMessageStatus(statusData);
        }
      }
    }
  }

  /// Traite un message entrant
  Future<void> _processIncomingMessage(
    Map<String, dynamic> messageData,
    Map<String, dynamic> value,
  ) async {
    try {
      final messageId = messageData['id'] as String;
      final from = messageData['from'] as String;
      final timestamp = messageData['timestamp'] as String;
      final type = messageData['type'] as String;

      // Obtenir le contenu du message selon le type
      String content = '';
      String? mediaUrl;
      String? mediaCaption;

      switch (type) {
        case 'text':
          content = messageData['text']['body'] as String;
          break;
        case 'image':
          final imageData = messageData['image'] as Map<String, dynamic>;
          mediaUrl = await _downloadMedia(imageData['id']);
          mediaCaption = imageData['caption'] as String?;
          content = mediaCaption ?? '[Image]';
          break;
        case 'document':
          final docData = messageData['document'] as Map<String, dynamic>;
          mediaUrl = await _downloadMedia(docData['id']);
          content = '[Document: ${docData['filename'] ?? 'Fichier'}]';
          break;
        case 'audio':
          final audioData = messageData['audio'] as Map<String, dynamic>;
          mediaUrl = await _downloadMedia(audioData['id']);
          content = '[Message vocal]';
          break;
        default:
          content = '[Message non supporté: $type]';
      }

      // Obtenir les informations du contact
      final contacts = value['contacts'] as List?;
      String customerName = from;
      if (contacts != null && contacts.isNotEmpty) {
        final contact = contacts.first as Map<String, dynamic>;
        final profile = contact['profile'] as Map<String, dynamic>?;
        customerName = profile?['name'] as String? ?? from;
      }

      // Créer ou obtenir le chat
      WhatsAppChat? chat = _whatsappService.getChatById('chat_$from');
      chat ??= await _whatsappService.createChat(
        customerName: customerName,
        customerPhone: from,
      );

      // Détecter l'intention du message
      final intention = _detectIntention(content);

      // Créer le message
      final message = WhatsAppMessage(
        id: messageId,
        chatId: chat.id,
        content: content,
        type: _mapMessageType(type),
        status: MessageStatus.delivered,
        timestamp: DateTime.fromMillisecondsSinceEpoch(
          int.parse(timestamp) * 1000,
        ),
        isFromCustomer: true,
        mediaUrl: mediaUrl,
        mediaCaption: mediaCaption,
        detectedIntention: intention,
      );

      // Sauvegarder le message
      await _whatsappService.addMessage(message);

      // Générer une réponse automatique si l'IA est activée
      if (chat.isAiEnabled) {
        await _generateAutoResponse(chat, message);
      }
    } catch (e) {
      debugPrint('Erreur lors du traitement du message entrant: $e');
    }
  }

  /// Traite le statut d'un message
  Future<void> _processMessageStatus(Map<String, dynamic> statusData) async {
    try {
      final messageId = statusData['id'] as String;
      final status = statusData['status'] as String;
      final timestamp = statusData['timestamp'] as String;

      // Mettre à jour le statut du message dans la base de données locale
      await _whatsappService.updateMessageStatus(
        messageId,
        _mapMessageStatus(status),
      );
      debugPrint(
        'Statut du message $messageId: $status mis à jour à ${DateTime.fromMillisecondsSinceEpoch(int.parse(timestamp) * 1000)}',
      );
    } catch (e) {
      debugPrint('Erreur lors du traitement du statut: $e');
    }
  }

  /// Télécharge un média depuis WhatsApp et le sauvegarde localement
  Future<String?> _downloadMedia(String mediaId) async {
    try {
      // Obtenir les informations du média
      final response = await http.get(
        Uri.parse('https://graph.facebook.com/v18.0/$mediaId'),
        headers: {'Authorization': 'Bearer $_accessToken'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final mediaUrl = data['url'] as String;
        final mimeType =
            data['mime_type'] as String? ?? 'application/octet-stream';
        final fileSize = data['file_size'] as int? ?? 0;

        // Télécharger le fichier média
        final mediaResponse = await http.get(
          Uri.parse(mediaUrl),
          headers: {'Authorization': 'Bearer $_accessToken'},
        );

        if (mediaResponse.statusCode == 200) {
          // Sauvegarder le fichier localement
          return await _saveMediaLocally(
            mediaResponse.bodyBytes,
            mimeType,
            fileSize,
            mediaId,
          );
        }
      }
    } catch (e) {
      debugPrint('Erreur lors du téléchargement du média: $e');
    }
    return null;
  }

  /// Sauvegarde un média téléchargé localement
  Future<String?> _saveMediaLocally(
    Uint8List bytes,
    String mimeType,
    int fileSize,
    String mediaId,
  ) async {
    try {
      // Déterminer l'extension du fichier basée sur le MIME type
      String extension = _getFileExtensionFromMimeType(mimeType);

      // Déterminer le type de média
      media_service.MediaType mediaType = _getMediaTypeFromMimeType(mimeType);

      // Créer un nom de fichier unique
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'whatsapp_${mediaId}_$timestamp$extension';

      // Créer un objet MediaFile
      final mediaFile = media_service.MediaFile(
        path: '', // Sera défini par saveMediaFile
        name: fileName,
        size: fileSize > 0 ? fileSize : bytes.length,
        mimeType: mimeType,
        type: mediaType,
        bytes: bytes,
      );

      // Sauvegarder le fichier en utilisant le MediaService
      final savedPath = await _mediaService.saveMediaFile(mediaFile);

      if (savedPath != null) {
        debugPrint('Média sauvegardé localement: $savedPath');
        return savedPath;
      } else {
        debugPrint('Échec de la sauvegarde locale du média');
        return null;
      }
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde locale: $e');
      return null;
    }
  }

  /// Détermine l'extension du fichier basée sur le MIME type
  String _getFileExtensionFromMimeType(String mimeType) {
    switch (mimeType.toLowerCase()) {
      // Images
      case 'image/jpeg':
      case 'image/jpg':
        return '.jpg';
      case 'image/png':
        return '.png';
      case 'image/gif':
        return '.gif';
      case 'image/webp':
        return '.webp';

      // Vidéos
      case 'video/mp4':
        return '.mp4';
      case 'video/avi':
        return '.avi';
      case 'video/mov':
        return '.mov';
      case 'video/quicktime':
        return '.mov';

      // Audio
      case 'audio/mpeg':
      case 'audio/mp3':
        return '.mp3';
      case 'audio/wav':
        return '.wav';
      case 'audio/ogg':
        return '.ogg';
      case 'audio/aac':
        return '.aac';
      case 'audio/amr':
        return '.amr';

      // Documents
      case 'application/pdf':
        return '.pdf';
      case 'application/msword':
        return '.doc';
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return '.docx';
      case 'application/vnd.ms-excel':
        return '.xls';
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return '.xlsx';
      case 'text/plain':
        return '.txt';

      default:
        return '.bin'; // Extension générique pour les fichiers inconnus
    }
  }

  /// Détermine le type de média basé sur le MIME type
  media_service.MediaType _getMediaTypeFromMimeType(String mimeType) {
    if (mimeType.startsWith('image/')) {
      return media_service.MediaType.image;
    } else if (mimeType.startsWith('video/')) {
      return media_service.MediaType.video;
    } else if (mimeType.startsWith('audio/')) {
      return media_service.MediaType.audio;
    } else {
      return media_service.MediaType.document;
    }
  }

  /// Détecte l'intention d'un message
  CustomerIntention _detectIntention(String content) {
    final lowercaseContent = content.toLowerCase();

    // Mots-clés pour l'achat
    final buyKeywords = [
      'acheter',
      'prix',
      'coût',
      'combien',
      'commander',
      'disponible',
      'stock',
    ];
    if (buyKeywords.any((keyword) => lowercaseContent.contains(keyword))) {
      return CustomerIntention.achat;
    }

    // Mots-clés pour le support
    final supportKeywords = [
      'problème',
      'bug',
      'erreur',
      'aide',
      'support',
      'réparer',
      'cassé',
    ];
    if (supportKeywords.any((keyword) => lowercaseContent.contains(keyword))) {
      return CustomerIntention.support;
    }

    // Mots-clés pour l'information
    final infoKeywords = [
      'information',
      'comment',
      'quand',
      'où',
      'livraison',
      'garantie',
    ];
    if (infoKeywords.any((keyword) => lowercaseContent.contains(keyword))) {
      return CustomerIntention.information;
    }

    return CustomerIntention.unknown;
  }

  /// Mappe le type de message WhatsApp vers notre enum
  MessageType _mapMessageType(String whatsappType) {
    switch (whatsappType) {
      case 'text':
        return MessageType.text;
      case 'image':
        return MessageType.image;
      case 'document':
        return MessageType.document;
      case 'audio':
        return MessageType.audio;
      case 'video':
        return MessageType.video;
      case 'location':
        return MessageType.location;
      case 'contact':
        return MessageType.contact;
      default:
        return MessageType.text;
    }
  }

  /// Mappe le statut de message WhatsApp vers notre enum
  MessageStatus _mapMessageStatus(String whatsappStatus) {
    switch (whatsappStatus) {
      case 'sent':
        return MessageStatus.sent;
      case 'delivered':
        return MessageStatus.delivered;
      case 'read':
        return MessageStatus.read;
      case 'failed':
        return MessageStatus.failed;
      default:
        return MessageStatus.sent;
    }
  }

  /// Génère une réponse automatique
  Future<void> _generateAutoResponse(
    WhatsAppChat chat,
    WhatsAppMessage incomingMessage,
  ) async {
    try {
      // Délai avant réponse (pour paraître naturel)
      await Future.delayed(const Duration(seconds: 2));

      String responseContent = '';
      String? aiModel;

      // Essayer d'abord de trouver une réponse dans la base de connaissances
      final knowledgeAnswer = _knowledgeService.findBestAnswer(
        incomingMessage.content,
      );
      if (knowledgeAnswer != null) {
        responseContent = knowledgeAnswer;
      } else {
        // Sinon, utiliser l'IA pour générer une réponse
        final aiResponse = await _aiService.generateResponse(
          message: incomingMessage.content,
          context: 'Conversation WhatsApp avec ${chat.customerName}',
          intention: incomingMessage.detectedIntention,
          conversationHistory:
              _whatsappService.getMessagesForChat(chat.id).take(5).toList(),
        );

        if (aiResponse != null) {
          responseContent = aiResponse.content;
          aiModel = aiResponse.model.modelName;
        } else {
          // Réponse par défaut
          responseContent = _getDefaultResponse(
            incomingMessage.detectedIntention,
          );
        }
      }

      // Créer le message de réponse
      final responseMessage = WhatsAppMessage(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}_auto',
        chatId: chat.id,
        content: responseContent,
        type: MessageType.text,
        status: MessageStatus.sent,
        timestamp: DateTime.now(),
        isFromCustomer: false,
        isAiGenerated: true,
        aiModel: aiModel,
      );

      // Sauvegarder la réponse
      await _whatsappService.addMessage(responseMessage);

      // Envoyer la réponse via l'API WhatsApp (seulement si configuré)
      if (_isConfigured) {
        await _sendMessage(chat.customerPhone, responseContent);
      }
    } catch (e) {
      debugPrint('Erreur lors de la génération de réponse automatique: $e');
    }
  }

  /// Obtient une réponse par défaut selon l'intention
  String _getDefaultResponse(CustomerIntention? intention) {
    switch (intention) {
      case CustomerIntention.achat:
        return 'Merci pour votre intérêt ! Un de nos conseillers va vous contacter rapidement pour vous aider avec votre achat.';
      case CustomerIntention.support:
        return 'Je comprends votre problème. Notre équipe support va examiner votre demande et vous répondre dans les plus brefs délais.';
      case CustomerIntention.information:
        return 'Merci pour votre question ! Je vais chercher ces informations pour vous et revenir vers vous rapidement.';
      default:
        return 'Merci pour votre message ! Un membre de notre équipe va vous répondre prochainement.';
    }
  }

  /// Envoie un message via l'API WhatsApp
  Future<bool> _sendMessage(
    String to,
    String content, {
    WhatsAppMessage? originalMessage,
  }) async {
    if (!_isConfigured) return false;

    try {
      Map<String, dynamic> messageData = {
        'messaging_product': 'whatsapp',
        'to': to,
      };

      // Déterminer le type de message à envoyer
      if (originalMessage != null && originalMessage.type != MessageType.text) {
        return await _sendMediaMessage(to, originalMessage);
      } else {
        // Message texte
        messageData.addAll({
          'type': 'text',
          'text': {'body': content},
        });
      }

      final response = await http.post(
        Uri.parse('https://graph.facebook.com/v18.0/$_phoneNumberId/messages'),
        headers: {
          'Authorization': 'Bearer $_accessToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(messageData),
      );

      if (response.statusCode == 200) {
        debugPrint('Message envoyé avec succès à $to');
        return true;
      } else {
        debugPrint('Erreur lors de l\'envoi du message: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du message: $e');
      return false;
    }
  }

  /// Envoie un message média via l'API WhatsApp
  Future<bool> _sendMediaMessage(String to, WhatsAppMessage message) async {
    if (!_isConfigured || message.mediaUrl == null) return false;

    try {
      // D'abord, uploader le média
      final mediaId = await _uploadMedia(
        message.mediaUrl!,
        message.mimeType ?? 'application/octet-stream',
      );
      if (mediaId == null) {
        debugPrint('Échec de l\'upload du média');
        return false;
      }

      // Construire le message selon le type
      Map<String, dynamic> messageData = {
        'messaging_product': 'whatsapp',
        'to': to,
      };

      switch (message.type) {
        case MessageType.image:
          messageData.addAll({
            'type': 'image',
            'image': {
              'id': mediaId,
              if (message.mediaCaption != null &&
                  message.mediaCaption!.isNotEmpty)
                'caption': message.mediaCaption,
            },
          });
          break;

        case MessageType.video:
          messageData.addAll({
            'type': 'video',
            'video': {
              'id': mediaId,
              if (message.mediaCaption != null &&
                  message.mediaCaption!.isNotEmpty)
                'caption': message.mediaCaption,
            },
          });
          break;

        case MessageType.audio:
          messageData.addAll({
            'type': 'audio',
            'audio': {'id': mediaId},
          });
          break;

        case MessageType.document:
          messageData.addAll({
            'type': 'document',
            'document': {
              'id': mediaId,
              if (message.fileName != null) 'filename': message.fileName,
              if (message.mediaCaption != null &&
                  message.mediaCaption!.isNotEmpty)
                'caption': message.mediaCaption,
            },
          });
          break;

        default:
          return false;
      }

      final response = await http.post(
        Uri.parse('https://graph.facebook.com/v18.0/$_phoneNumberId/messages'),
        headers: {
          'Authorization': 'Bearer $_accessToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(messageData),
      );

      if (response.statusCode == 200) {
        debugPrint('Message média envoyé avec succès à $to');
        return true;
      } else {
        debugPrint(
          'Erreur lors de l\'envoi du message média: ${response.body}',
        );
        return false;
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du message média: $e');
      return false;
    }
  }

  /// Upload un fichier média vers WhatsApp
  Future<String?> _uploadMedia(String filePath, String mimeType) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('Fichier non trouvé: $filePath');
        return null;
      }

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('https://graph.facebook.com/v18.0/$_phoneNumberId/media'),
      );

      request.headers.addAll({'Authorization': 'Bearer $_accessToken'});

      request.fields['messaging_product'] = 'whatsapp';
      request.fields['type'] = mimeType;

      request.files.add(
        await http.MultipartFile.fromPath(
          'file',
          filePath,
          contentType: MediaType.parse(mimeType),
        ),
      );

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        final mediaId = data['id'] as String?;
        debugPrint('Média uploadé avec succès, ID: $mediaId');
        return mediaId;
      } else {
        debugPrint('Erreur lors de l\'upload du média: $responseBody');
        return null;
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'upload du média: $e');
      return null;
    }
  }

  /// Configure les paramètres WhatsApp Business API
  Future<void> updateConfiguration({
    required String accessToken,
    required String phoneNumberId,
    required String verifyToken,
    required String webhookUrl,
  }) async {
    _accessToken = accessToken;
    _phoneNumberId = phoneNumberId;
    _verifyToken = verifyToken;
    _webhookUrl = webhookUrl;
    _isConfigured = accessToken.isNotEmpty && phoneNumberId.isNotEmpty;

    await _saveConfiguration();
  }

  /// Sauvegarde la configuration
  Future<void> _saveConfiguration() async {
    final prefs = await SharedPreferences.getInstance();
    final config = {
      'accessToken': _accessToken,
      'phoneNumberId': _phoneNumberId,
      'verifyToken': _verifyToken,
      'webhookUrl': _webhookUrl,
      'isConfigured': _isConfigured,
    };
    await prefs.setString(_configKey, jsonEncode(config));
  }

  /// Charge la configuration
  Future<void> _loadConfiguration() async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = prefs.getString(_configKey);
    if (configJson != null) {
      final config = jsonDecode(configJson) as Map<String, dynamic>;
      _accessToken = config['accessToken'] ?? '';
      _phoneNumberId = config['phoneNumberId'] ?? '';
      _verifyToken = config['verifyToken'] ?? '';
      _webhookUrl = config['webhookUrl'] ?? '';
      _isConfigured = config['isConfigured'] ?? false;
    }
  }

  // Getters
  bool get isServerRunning => _isServerRunning;
  bool get isConfigured => _isConfigured;
  String get webhookUrl => _webhookUrl;
  String get accessToken => _accessToken;
  String get phoneNumberId => _phoneNumberId;
  String get verifyToken => _verifyToken;
}
