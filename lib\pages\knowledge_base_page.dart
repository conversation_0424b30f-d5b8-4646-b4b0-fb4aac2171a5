import 'package:flutter/material.dart';
import '../models/ai_models.dart';
import '../services/knowledge_service.dart';

class KnowledgeBasePage extends StatefulWidget {
  const KnowledgeBasePage({super.key});

  @override
  State<KnowledgeBasePage> createState() => _KnowledgeBasePageState();
}

class _KnowledgeBasePageState extends State<KnowledgeBasePage>
    with TickerProviderStateMixin {
  final KnowledgeService _knowledgeService = KnowledgeService();
  final TextEditingController _searchController = TextEditingController();

  List<KnowledgeArticle> _articles = [];
  List<KnowledgeArticle> _filteredArticles = [];
  List<String> _categories = [];
  String _selectedCategory = 'Toutes';
  bool _isLoading = true;

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadKnowledgeBase();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadKnowledgeBase() async {
    setState(() => _isLoading = true);

    try {
      await _knowledgeService.initialize();
      final articles = _knowledgeService.getAllArticles();
      final categories = _knowledgeService.getCategories();

      setState(() {
        _articles = articles;
        _filteredArticles = articles;
        _categories = ['Toutes', ...categories];
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterArticles() {
    final query = _searchController.text.toLowerCase();

    setState(() {
      _filteredArticles =
          _articles.where((article) {
            final matchesSearch =
                query.isEmpty ||
                article.title.toLowerCase().contains(query) ||
                article.content.toLowerCase().contains(query) ||
                article.tags.any((tag) => tag.toLowerCase().contains(query));

            final matchesCategory =
                _selectedCategory == 'Toutes' ||
                article.category == _selectedCategory;

            return matchesSearch && matchesCategory;
          }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Base de Connaissances'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.add), onPressed: _addNewArticle),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showStatistics,
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildSearchAndFilter(),
              Expanded(
                child:
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : _buildArticlesList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.blue[50],
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            onChanged: (_) => _filterArticles(),
            decoration: InputDecoration(
              hintText: 'Rechercher dans la base de connaissances...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchController.text.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _filterArticles();
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children:
                  _categories.map((category) {
                    final isSelected = category == _selectedCategory;
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                          _filterArticles();
                        },
                        backgroundColor: Colors.white,
                        selectedColor: Colors.blue[100],
                        checkmarkColor: Colors.blue[700],
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildArticlesList() {
    if (_filteredArticles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.article_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchController.text.isNotEmpty
                  ? 'Aucun article trouvé'
                  : 'Aucun article dans la base',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchController.text.isNotEmpty
                  ? 'Essayez un autre terme de recherche'
                  : 'Commencez par ajouter des articles',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredArticles.length,
      itemBuilder: (context, index) {
        final article = _filteredArticles[index];
        return _buildArticleCard(article);
      },
    );
  }

  Widget _buildArticleCard(KnowledgeArticle article) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _viewArticle(article),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      article.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getCategoryColor(
                        article.category,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      article.category,
                      style: TextStyle(
                        fontSize: 12,
                        color: _getCategoryColor(article.category),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                _getArticlePreview(article.content),
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.star, size: 16, color: Colors.amber[600]),
                  const SizedBox(width: 4),
                  Text(
                    article.relevanceScore.toStringAsFixed(1),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.access_time, size: 16, color: Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(article.updatedAt),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleArticleAction(value, article),
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 16),
                                SizedBox(width: 8),
                                Text('Modifier'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 16, color: Colors.red),
                                SizedBox(width: 8),
                                Text(
                                  'Supprimer',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                  ),
                ],
              ),
              if (article.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children:
                      article.tags.take(3).map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '#$tag',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[700],
                            ),
                          ),
                        );
                      }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Produits':
        return Colors.blue;
      case 'Service Client':
        return Colors.green;
      case 'Support Technique':
        return Colors.orange;
      case 'Livraison':
        return Colors.purple;
      case 'Paiement':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _getArticlePreview(String content) {
    final lines =
        content.split('\n').where((line) => line.trim().isNotEmpty).toList();
    if (lines.isEmpty) return '';

    // Prendre la première ligne non-titre
    for (final line in lines) {
      if (!line.startsWith('**') && !line.startsWith('#') && line.length > 20) {
        return line.trim();
      }
    }

    return lines.first.trim();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Aujourd\'hui';
    } else if (difference.inDays == 1) {
      return 'Hier';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _viewArticle(KnowledgeArticle article) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 600, maxHeight: 600),
              child: Column(
                children: [
                  AppBar(
                    title: Text(article.title),
                    backgroundColor: _getCategoryColor(article.category),
                    foregroundColor: Colors.white,
                    automaticallyImplyLeading: false,
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        article.content,
                        style: const TextStyle(fontSize: 14, height: 1.5),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _handleArticleAction(String action, KnowledgeArticle article) {
    switch (action) {
      case 'edit':
        _editArticle(article);
        break;
      case 'delete':
        _deleteArticle(article);
        break;
    }
  }

  void _addNewArticle() {
    showDialog(
      context: context,
      builder:
          (context) => _ArticleEditorDialog(
            onSave: (article) async {
              await _knowledgeService.addArticle(article);
              await _loadKnowledgeBase();
              if (mounted) {
                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Article ajouté avec succès'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
          ),
    );
  }

  void _editArticle(KnowledgeArticle article) {
    showDialog(
      context: context,
      builder:
          (context) => _ArticleEditorDialog(
            article: article,
            onSave: (updatedArticle) async {
              await _knowledgeService.updateArticle(updatedArticle);
              await _loadKnowledgeBase();
              if (mounted) {
                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Article modifié avec succès'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
          ),
    );
  }

  void _deleteArticle(KnowledgeArticle article) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer l\'article'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer "${article.title}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );

    if (confirm == true) {
      await _knowledgeService.deleteArticle(article.id);
      await _loadKnowledgeBase();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Article supprimé'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _showStatistics() {
    final stats = _knowledgeService.getStatistics();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Statistiques de la Base'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Articles totaux: ${stats['totalArticles']}'),
                Text('Catégories: ${stats['categoriesCount']}'),
                Text(
                  'Pertinence moyenne: ${(stats['averageRelevance'] as double).toStringAsFixed(2)}',
                ),
                const SizedBox(height: 16),
                const Text(
                  'Par catégorie:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...(stats['categoryStats'] as Map<String, int>).entries.map(
                  (entry) => Text('• ${entry.key}: ${entry.value}'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }
}

class _ArticleEditorDialog extends StatefulWidget {
  final KnowledgeArticle? article;
  final Function(KnowledgeArticle) onSave;

  const _ArticleEditorDialog({this.article, required this.onSave});

  @override
  State<_ArticleEditorDialog> createState() => _ArticleEditorDialogState();
}

class _ArticleEditorDialogState extends State<_ArticleEditorDialog> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final TextEditingController _tagsController = TextEditingController();
  final TextEditingController _keywordsController = TextEditingController();
  final TextEditingController _categoryController = TextEditingController();
  final TextEditingController _relevanceController = TextEditingController();

  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    if (widget.article != null) {
      _titleController.text = widget.article!.title;
      _contentController.text = widget.article!.content;
      _tagsController.text = widget.article!.tags.join(', ');
      _keywordsController.text = widget.article!.keywords.join(', ');
      _categoryController.text = widget.article!.category;
      _relevanceController.text = widget.article!.relevanceScore.toString();
    } else {
      _relevanceController.text = '0.8';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagsController.dispose();
    _keywordsController.dispose();
    _categoryController.dispose();
    _relevanceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        child: Column(
          children: [
            AppBar(
              title: Text(
                widget.article != null
                    ? 'Modifier l\'article'
                    : 'Nouvel article',
              ),
              backgroundColor: Colors.blue[700],
              foregroundColor: Colors.white,
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Titre *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _categoryController,
                      decoration: const InputDecoration(
                        labelText: 'Catégorie *',
                        border: OutlineInputBorder(),
                        hintText: 'ex: Support Technique',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _contentController,
                      decoration: const InputDecoration(
                        labelText: 'Contenu *',
                        border: OutlineInputBorder(),
                        hintText: 'Contenu de l\'article en Markdown...',
                      ),
                      maxLines: 10,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _tagsController,
                      decoration: const InputDecoration(
                        labelText: 'Tags',
                        border: OutlineInputBorder(),
                        hintText: 'tag1, tag2, tag3',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _keywordsController,
                      decoration: const InputDecoration(
                        labelText: 'Mots-clés',
                        border: OutlineInputBorder(),
                        hintText: 'mot1, mot2, mot3',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _relevanceController,
                      decoration: const InputDecoration(
                        labelText: 'Score de pertinence (0.0 - 1.0)',
                        border: OutlineInputBorder(),
                        hintText: '0.8',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: _isSaving ? null : _saveArticle,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[700],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child:
                          _isSaving
                              ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('Sauvegarde...'),
                                ],
                              )
                              : Text(
                                widget.article != null ? 'Modifier' : 'Créer',
                              ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveArticle() async {
    final title = _titleController.text.trim();
    final content = _contentController.text.trim();
    final category = _categoryController.text.trim();

    if (title.isEmpty || content.isEmpty || category.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez remplir tous les champs obligatoires'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isSaving = true);

    try {
      final tags =
          _tagsController.text
              .split(',')
              .map((tag) => tag.trim())
              .where((tag) => tag.isNotEmpty)
              .toList();

      final keywords =
          _keywordsController.text
              .split(',')
              .map((keyword) => keyword.trim())
              .where((keyword) => keyword.isNotEmpty)
              .toList();

      final relevanceScore = double.tryParse(_relevanceController.text) ?? 0.8;

      final article = KnowledgeArticle(
        id:
            widget.article?.id ??
            'art_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        content: content,
        tags: tags,
        keywords: keywords,
        category: category,
        relevanceScore: relevanceScore.clamp(0.0, 1.0),
        createdAt: widget.article?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      widget.onSave(article);
      Navigator.pop(context);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sauvegarde: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }
}


