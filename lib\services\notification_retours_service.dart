import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/colis.dart';
import 'colis_service.dart';

class NotificationRetoursService {
  static final NotificationRetoursService _instance = NotificationRetoursService._internal();
  factory NotificationRetoursService() => _instance;
  NotificationRetoursService._internal();

  static NotificationRetoursService get instance => _instance;

  Timer? _timer;
  final ColisService _colisService = ColisService.instance;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;
  
  // Callback pour notifier les changements
  Function(int)? onRetoursCountChanged;

  Future<void> _initializeNotifications() async {
    if (_isInitialized) return;
    
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    
    await _flutterLocalNotificationsPlugin.initialize(initializationSettings);
    _isInitialized = true;
  }

  Future<void> startPeriodicNotifications() async {
    await _initializeNotifications();
    
    // Vérifier immédiatement
    _checkRetours();
    
    // Puis vérifier toutes les 3 heures (3 * 60 * 60 * 1000 ms)
    _timer = Timer.periodic(const Duration(hours: 3), (timer) {
      _checkRetours();
    });
  }

  void stopPeriodicNotifications() {
    _timer?.cancel();
    _timer = null;
  }

  Future<void> _checkRetours() async {
    try {
      final retours = await _colisService.obtenirColisParStatut(StatutLivraison.retour);
      final nombreRetours = retours.length;
      
      // Notifier le changement du nombre de retours
      onRetoursCountChanged?.call(nombreRetours);
      
      // Si il y a des retours, envoyer une notification
      if (nombreRetours > 0) {
        _sendNotification(nombreRetours);
      }
    } catch (e) {
      debugPrint('Erreur lors de la vérification des retours: $e');
    }
  }

  Future<void> _sendNotification(int nombreRetours) async {
    if (!_isInitialized) {
      await _initializeNotifications();
    }
    
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'retours_channel',
      'Notifications de retours',
      channelDescription: 'Notifications pour les colis en retour',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: false,
    );
    
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );
    
    final String title = nombreRetours == 1 
        ? 'Retour en attente'
        : 'Retours en attente';
    final String body = nombreRetours == 1
        ? '$nombreRetours colis en retour nécessite votre attention'
        : '$nombreRetours colis en retour nécessitent votre attention';
    
    await _flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
    );
    
    debugPrint('Notification envoyée: $nombreRetours retour(s) en attente');
  }

  Future<int> getRetoursCount() async {
    try {
      final retours = await _colisService.obtenirColisParStatut(StatutLivraison.retour);
      return retours.length;
    } catch (e) {
      debugPrint('Erreur lors du comptage des retours: $e');
      return 0;
    }
  }

  void dispose() {
    stopPeriodicNotifications();
    onRetoursCountChanged = null;
  }
}