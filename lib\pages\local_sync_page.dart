import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/local_sync_service.dart';

class LocalSyncPage extends StatefulWidget {
  const LocalSyncPage({super.key});

  @override
  State<LocalSyncPage> createState() => _LocalSyncPageState();
}

class _LocalSyncPageState extends State<LocalSyncPage>
    with TickerProviderStateMixin {
  final LocalSyncService _syncService = LocalSyncService();

  bool _isServerRunning = false;
  bool _isScanning = false;
  bool _isSyncing = false;
  String? _localIP;
  List<Map<String, dynamic>> _discoveredDevices = [];

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkServerStatus();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _checkServerStatus() async {
    setState(() {
      _isServerRunning = _syncService.isServerRunning;
      _localIP = _syncService.localIP;
    });
  }

  Future<void> _toggleServer() async {
    if (_isServerRunning) {
      await _syncService.stopSyncServer();
      setState(() {
        _isServerRunning = false;
        _localIP = null;
      });
      _showSnackBar('Serveur arrêté', Colors.orange);
    } else {
      final success = await _syncService.startSyncServer();
      if (success) {
        setState(() {
          _isServerRunning = true;
          _localIP = _syncService.localIP;
        });
        _showSnackBar('Serveur démarré sur $_localIP:8080', Colors.green);
      } else {
        _showSnackBar('Erreur lors du démarrage du serveur', Colors.red);
      }
    }
  }

  Future<void> _scanForDevices() async {
    setState(() {
      _isScanning = true;
      _discoveredDevices.clear();
    });

    try {
      final devices = await _syncService.discoverDevices();
      setState(() {
        _discoveredDevices = devices;
        _isScanning = false;
      });

      if (devices.isEmpty) {
        _showSnackBar('Aucun appareil trouvé sur le réseau', Colors.orange);
      } else {
        _showSnackBar('${devices.length} appareil(s) trouvé(s)', Colors.green);
      }
    } catch (e) {
      setState(() => _isScanning = false);
      _showSnackBar('Erreur lors de la recherche: $e', Colors.red);
    }
  }

  Future<void> _syncWithDevice(String ip) async {
    setState(() => _isSyncing = true);

    try {
      final result = await _syncService.syncWithDevice(ip);
      setState(() => _isSyncing = false);

      if (result.success) {
        _showSnackBar('Synchronisation réussie avec $ip', Colors.green);
      } else {
        String errorMessage = 'Échec de la synchronisation avec $ip';
        if (result.error != null) {
          errorMessage += '\n${result.error}';
        }
        if (result.details != null) {
          errorMessage += '\n${result.details}';
        }
        _showSnackBar(errorMessage, Colors.red);
      }
    } catch (e) {
      setState(() => _isSyncing = false);
      _showSnackBar('Erreur inattendue: $e', Colors.red);
    }
  }

  Future<void> _showNetworkDiagnostic() async {
    try {
      final diagnostic = await _syncService.networkDiagnostic();

      if (mounted) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Diagnostic Réseau'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildDiagnosticItem(
                        'IP Locale',
                        diagnostic['local_ip'] ?? 'Non disponible',
                      ),
                      _buildDiagnosticItem(
                        'Statut IP',
                        diagnostic['local_ip_status'] ?? 'Inconnu',
                      ),
                      _buildDiagnosticItem(
                        'Serveur',
                        diagnostic['server_running'] == true
                            ? 'Actif'
                            : 'Inactif',
                      ),
                      _buildDiagnosticItem(
                        'Port',
                        diagnostic['server_port']?.toString() ?? 'Inconnu',
                      ),
                      _buildDiagnosticItem(
                        'WiFi',
                        diagnostic['wifi_name'] ?? 'Non connecté',
                      ),
                      _buildDiagnosticItem(
                        'Statut WiFi',
                        diagnostic['wifi_status'] ?? 'Inconnu',
                      ),
                      _buildDiagnosticItem(
                        'Passerelle',
                        diagnostic['gateway_reachable'] == true
                            ? 'Accessible'
                            : 'Inaccessible',
                      ),
                      _buildDiagnosticItem(
                        'Appareils trouvés',
                        diagnostic['discovered_devices_count']?.toString() ??
                            '0',
                      ),
                      if (diagnostic['diagnostic_error'] != null)
                        _buildDiagnosticItem(
                          'Erreur',
                          diagnostic['diagnostic_error'],
                          isError: true,
                        ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Fermer'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      _showSnackBar('Erreur lors du diagnostic: $e', Colors.red);
    }
  }

  Widget _buildDiagnosticItem(
    String label,
    String value, {
    bool isError = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: isError ? Colors.red : null),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _testDeviceConnectivity(String ip) async {
    try {
      final test = await _syncService.testDeviceConnectivity(ip);

      if (mounted) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: Text('Test de Connectivité - $ip'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildDiagnosticItem(
                        'IP Cible',
                        test['target_ip'] ?? 'Inconnu',
                      ),
                      _buildDiagnosticItem(
                        'Ping',
                        test['ping_success'] == true ? 'Réussi' : 'Échoué',
                      ),
                      if (test['ping_duration_ms'] != null)
                        _buildDiagnosticItem(
                          'Durée Ping',
                          '${test['ping_duration_ms']} ms',
                        ),
                      if (test['ping_error'] != null)
                        _buildDiagnosticItem(
                          'Erreur Ping',
                          test['ping_error'],
                          isError: true,
                        ),
                      _buildDiagnosticItem(
                        'Service Sync',
                        test['sync_service_available'] == true
                            ? 'Disponible'
                            : 'Indisponible',
                      ),
                      if (test['sync_duration_ms'] != null)
                        _buildDiagnosticItem(
                          'Durée Sync',
                          '${test['sync_duration_ms']} ms',
                        ),
                      if (test['sync_error'] != null)
                        _buildDiagnosticItem(
                          'Erreur Sync',
                          test['sync_error'],
                          isError: true,
                        ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Fermer'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      _showSnackBar('Erreur lors du test: $e', Colors.red);
    }
  }

  void _showSnackBar(String message, Color color) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message, style: const TextStyle(fontSize: 13)),
          backgroundColor: color,
          duration: Duration(seconds: message.length > 100 ? 8 : 4),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          action:
              message.contains('Échec') || message.contains('Erreur')
                  ? SnackBarAction(
                    label: 'Détails',
                    textColor: Colors.white,
                    onPressed: () => _showErrorDialog(message),
                  )
                  : null,
        ),
      );
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                SizedBox(width: 8),
                Text('Détails de l\'erreur'),
              ],
            ),
            content: SingleChildScrollView(
              child: Text(message, style: const TextStyle(fontSize: 14)),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
              TextButton(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: message));
                  Navigator.of(context).pop();
                  _showSnackBar(
                    'Erreur copiée dans le presse-papiers',
                    Colors.blue,
                  );
                },
                child: const Text('Copier'),
              ),
            ],
          ),
    );
  }

  void _copyIPToClipboard() {
    if (_localIP != null) {
      Clipboard.setData(ClipboardData(text: '$_localIP:8080'));
      _showSnackBar('Adresse copiée dans le presse-papiers', Colors.blue);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Synchronisation Locale'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child:
            _isSyncing
                ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Synchronisation en cours...'),
                    ],
                  ),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildInfoCard(),
                      const SizedBox(height: 16),
                      _buildServerCard(),
                      const SizedBox(height: 16),
                      _buildDiagnosticCard(),
                      const SizedBox(height: 16),
                      _buildScanCard(),
                      if (_discoveredDevices.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildDevicesCard(),
                      ],
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Synchronisation Locale',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Synchronisez vos données avec d\'autres appareils sur le même réseau WiFi sans connexion Internet.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServerCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isServerRunning ? _pulseAnimation.value : 1.0,
                      child: Icon(
                        _isServerRunning ? Icons.wifi : Icons.wifi_off,
                        color:
                            _isServerRunning
                                ? Colors.green[700]
                                : Colors.grey[600],
                        size: 24,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                Text(
                  _isServerRunning ? 'Serveur Actif' : 'Serveur Inactif',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_isServerRunning && _localIP != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Adresse du serveur:',
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            '$_localIP:8080',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: _copyIPToClipboard,
                      icon: const Icon(Icons.copy),
                      tooltip: 'Copier l\'adresse',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],
            ElevatedButton.icon(
              onPressed: _toggleServer,
              icon: Icon(_isServerRunning ? Icons.stop : Icons.play_arrow),
              label: Text(
                _isServerRunning ? 'Arrêter le Serveur' : 'Démarrer le Serveur',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    _isServerRunning ? Colors.red[700] : Colors.green[700],
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 48),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.network_check, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Diagnostic Réseau',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Vérifiez la connectivité réseau et identifiez les problèmes de synchronisation.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showNetworkDiagnostic,
              icon: const Icon(Icons.network_check),
              label: const Text('Lancer le Diagnostic'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[700],
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 48),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.search, color: Colors.orange[700], size: 24),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Rechercher des Appareils',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                if (_discoveredDevices.isNotEmpty && !_isScanning)
                  IconButton(
                    onPressed: _scanForDevices,
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Actualiser la recherche',
                    color: Colors.orange[700],
                  ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Recherchez d\'autres appareils HCP-DESIGN sur votre réseau.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _isScanning ? null : _scanForDevices,
              icon:
                  _isScanning
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.search),
              label: Text(_isScanning ? 'Recherche...' : 'Rechercher'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[700],
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 48),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDevicesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.devices, color: Colors.purple[700], size: 24),
                const SizedBox(width: 12),
                Text(
                  'Appareils Trouvés (${_discoveredDevices.length})',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...(_discoveredDevices.map((device) => _buildDeviceItem(device))),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceItem(Map<String, dynamic> device) {
    final bool isOnline = device['status'] == 'online';
    final String version = device['app_version'] ?? 'Inconnue';

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isOnline ? Colors.green[50] : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isOnline ? Colors.green[200]! : Colors.grey[300]!,
        ),
      ),
      child: Row(
        children: [
          Stack(
            children: [
              Icon(
                Icons.phone_android,
                color: isOnline ? Colors.green[700] : Colors.grey[600],
                size: 28,
              ),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    color: isOnline ? Colors.green : Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        device['device_name'] ?? 'Appareil Inconnu',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: isOnline ? Colors.green[100] : Colors.red[100],
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        isOnline ? 'En ligne' : 'Hors ligne',
                        style: TextStyle(
                          color: isOnline ? Colors.green[800] : Colors.red[800],
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '${device['ip']}:${device['port']}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                if (version != 'Inconnue')
                  Text(
                    'Version: $version',
                    style: TextStyle(color: Colors.grey[600], fontSize: 11),
                  ),
              ],
            ),
          ),
          Column(
            children: [
              ElevatedButton(
                onPressed:
                    isOnline
                        ? () => _testDeviceConnectivity(device['ip'])
                        : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[700],
                  foregroundColor: Colors.white,
                  minimumSize: const Size(80, 32),
                ),
                child: const Text('Test', style: TextStyle(fontSize: 12)),
              ),
              const SizedBox(height: 4),
              ElevatedButton(
                onPressed:
                    isOnline ? () => _syncWithDevice(device['ip']) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple[700],
                  foregroundColor: Colors.white,
                  minimumSize: const Size(80, 32),
                ),
                child: const Text('Sync', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
