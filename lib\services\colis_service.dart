import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter/foundation.dart';
import '../models/colis.dart';

/// Service pour gérer les colis et livraisons
class ColisService {
  static const String _colisKey = 'hcp_colis';
  static const String _enableModuleKey = 'hcp_enable_livraison_module';
  static const Uuid _uuid = Uuid();

  /// Instance singleton
  static final ColisService _instance = ColisService._internal();
  factory ColisService() => _instance;
  ColisService._internal();

  /// Obtient l'instance singleton
  static ColisService get instance => _instance;

  /// Vérifie si le module de livraison est activé
  Future<bool> isModuleEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_enableModuleKey) ?? false;
  }

  /// Active ou désactive le module de livraison
  Future<void> setModuleEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_enableModuleKey, enabled);
  }

  /// Charge tous les colis depuis le stockage local
  Future<List<Colis>> loadColis() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final colisJson = prefs.getStringList(_colisKey) ?? [];

      return colisJson
          .map((jsonString) => Colis.fromJson(json.decode(jsonString)))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors du chargement des colis: $e');
      }
      return [];
    }
  }

  /// Alias pour loadColis() pour compatibilité avec sync_service
  Future<List<Colis>> getAllColis() async {
    return await loadColis();
  }

  /// Ajoute un colis (pour compatibilité avec sync_service)
  Future<String> addColis(Colis colis) async {
    try {
      final colisList = await loadColis();
      colisList.add(colis);
      await saveColis(colisList);
      return colis.id;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de l\'ajout du colis: $e');
      }
      throw Exception('Impossible d\'ajouter le colis');
    }
  }

  /// Met à jour un colis (pour compatibilité avec sync_service)
  Future<void> updateColis(Colis colis) async {
    await mettreAJourColis(colis);
  }

  /// Sauvegarde tous les colis dans le stockage local
  Future<void> saveColis(List<Colis> colis) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final colisJson =
          colis.map((coli) => json.encode(coli.toJson())).toList();

      await prefs.setStringList(_colisKey, colisJson);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la sauvegarde des colis: $e');
      }
      throw Exception('Impossible de sauvegarder les colis');
    }
  }

  /// Ajoute un nouveau colis
  Future<String> ajouterColis({
    required String libelle,
    required String photoPath,
    required String zoneLivraison,
    required String numeroClient,
    required double resteAPayer,
    required double fraisLivraison,
    required StatutLivraison statut,
    String? nomClient,
    String? adresseLivraison,
    String? notes,
  }) async {
    try {
      final colis = await loadColis();
      final nouveauColis = Colis(
        id: _uuid.v4(),
        libelle: libelle,
        photoPath: photoPath,
        zoneLivraison: zoneLivraison,
        numeroClient: numeroClient,
        resteAPayer: resteAPayer,
        fraisLivraison: fraisLivraison,
        dateAjout: DateTime.now(),
        statut: statut,
        nomClient: nomClient,
        adresseLivraison: adresseLivraison,
        notes: notes,
      );

      colis.add(nouveauColis);
      await saveColis(colis);

      return nouveauColis.id;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de l\'ajout du colis: $e');
      }
      throw Exception('Impossible d\'ajouter le colis');
    }
  }

  /// Met à jour un colis existant
  Future<void> mettreAJourColis(Colis colisModifie) async {
    try {
      final colis = await loadColis();
      final index = colis.indexWhere((c) => c.id == colisModifie.id);

      if (index != -1) {
        colis[index] = colisModifie;
        await saveColis(colis);
      } else {
        throw Exception('Colis non trouvé');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la mise à jour du colis: $e');
      }
      throw Exception('Impossible de mettre à jour le colis');
    }
  }

  /// Supprime un colis
  Future<void> supprimerColis(String colisId) async {
    try {
      final colis = await loadColis();
      colis.removeWhere((c) => c.id == colisId);
      await saveColis(colis);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la suppression du colis: $e');
      }
      throw Exception('Impossible de supprimer le colis');
    }
  }

  /// Obtient un colis par son ID
  Future<Colis?> obtenirColisParId(String colisId) async {
    try {
      final colis = await loadColis();
      return colis.firstWhere(
        (c) => c.id == colisId,
        orElse: () => throw Exception('Colis non trouvé'),
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la récupération du colis: $e');
      }
      return null;
    }
  }

  /// Obtient les colis d'une date spécifique
  Future<List<Colis>> obtenirColisParDate(DateTime date) async {
    try {
      final colis = await loadColis();
      return colis
          .where(
            (c) =>
                c.dateAjout.year == date.year &&
                c.dateAjout.month == date.month &&
                c.dateAjout.day == date.day,
          )
          .toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la récupération des colis par date: $e');
      }
      return [];
    }
  }

  /// Obtient les colis du jour
  Future<List<Colis>> obtenirColisDuJour() async {
    return await obtenirColisParDate(DateTime.now());
  }

  /// Met à jour le statut d'un colis
  Future<void> mettreAJourStatut(
    String colisId,
    StatutLivraison nouveauStatut,
  ) async {
    try {
      final colis = await loadColis();
      final index = colis.indexWhere((c) => c.id == colisId);

      if (index != -1) {
        colis[index] = colis[index].copyWith(statut: nouveauStatut);
        await saveColis(colis);
      } else {
        throw Exception('Colis non trouvé');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la mise à jour du statut: $e');
      }
      throw Exception('Impossible de mettre à jour le statut');
    }
  }

  /// Alias pour mettreAJourStatut (compatibilité)
  Future<void> updateStatus(
    String colisId,
    StatutLivraison nouveauStatut,
  ) async {
    return await mettreAJourStatut(colisId, nouveauStatut);
  }



  /// Obtient les données pour le dashboard
  Future<Map<String, dynamic>> getDashboardData() async {
    try {
      final colis = await loadColis();
      final today = DateTime.now();
      final colisDuJour =
          colis
              .where(
                (c) =>
                    c.dateAjout.year == today.year &&
                    c.dateAjout.month == today.month &&
                    c.dateAjout.day == today.day,
              )
              .toList();

      // Calcul des statistiques
      double totalDailyCost = 0;
      double dailyProfit = 0;
      Map<String, double> amountsByZone = {};
      DateTime? lastDeliveryDate;

      for (final coli in colisDuJour) {
        totalDailyCost += coli.fraisLivraison;
        // Bénéfice du jour = total encaissé - frais de livraison
        dailyProfit += (coli.resteAPayer - coli.fraisLivraison);

        if (amountsByZone.containsKey(coli.zoneLivraison)) {
          amountsByZone[coli.zoneLivraison] =
              amountsByZone[coli.zoneLivraison]! + coli.fraisLivraison;
        } else {
          amountsByZone[coli.zoneLivraison] = coli.fraisLivraison;
        }

        if (lastDeliveryDate == null ||
            coli.dateAjout.isAfter(lastDeliveryDate)) {
          lastDeliveryDate = coli.dateAjout;
        }
      }

      return {
        'totalDailyCost': totalDailyCost,
        'dailyProfit': dailyProfit,
        'amountsByZone': amountsByZone,
        'lastDeliveryDate': lastDeliveryDate?.toString().split(' ')[0],
        'totalColis': colisDuJour.length,
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la récupération des données dashboard: $e');
      }
      return {
        'totalDailyCost': 0.0,
        'dailyProfit': 0.0,
        'amountsByZone': <String, double>{},
        'lastDeliveryDate': null,
        'totalColis': 0,
      };
    }
  }

  /// Associe un colis à une facture
  Future<void> associerFacture(String colisId, String factureId) async {
    try {
      final colis = await loadColis();
      final index = colis.indexWhere((c) => c.id == colisId);

      if (index != -1) {
        colis[index] = colis[index].copyWith(factureId: factureId);
        await saveColis(colis);
      } else {
        throw Exception('Colis non trouvé');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de l\'association à la facture: $e');
      }
      throw Exception('Impossible d\'associer le colis à la facture');
    }
  }

  /// Obtient les statistiques de livraison pour une date
  Future<StatistiquesLivraison> obtenirStatistiques(DateTime date) async {
    try {
      final colis = await loadColis();
      return StatistiquesLivraison.fromColis(colis, date);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors du calcul des statistiques: $e');
      }
      return StatistiquesLivraison(
        totalColis: 0,
        colisLivres: 0,
        colisEnRetard: 0,
        colisAnnules: 0,
        colisReportes: 0,
        colisEnCours: 0,
        totalResteAPayer: 0,
        totalFraisLivraison: 0,
        pointJournalier: 0,
        date: date,
        repartitionParZone: {},
      );
    }
  }

  /// Obtient les statistiques du jour
  Future<StatistiquesLivraison> obtenirStatistiquesDuJour() async {
    return await obtenirStatistiques(DateTime.now());
  }

  /// Obtient les colis par statut
  Future<List<Colis>> obtenirColisParStatut(StatutLivraison statut) async {
    try {
      final colis = await loadColis();
      return colis.where((c) => c.statut == statut).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la récupération des colis par statut: $e');
      }
      return [];
    }
  }

  /// Obtient les colis par zone de livraison
  Future<List<Colis>> obtenirColisParZone(String zone) async {
    try {
      final colis = await loadColis();
      return colis.where((c) => c.zoneLivraison == zone).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la récupération des colis par zone: $e');
      }
      return [];
    }
  }

  /// Recherche des colis par critères
  Future<List<Colis>> rechercherColis({
    String? libelle,
    String? numeroClient,
    String? nomClient,
    String? zoneLivraison,
    StatutLivraison? statut,
    DateTime? dateDebut,
    DateTime? dateFin,
  }) async {
    try {
      final colis = await loadColis();

      return colis.where((c) {
        bool correspond = true;

        if (libelle != null && libelle.isNotEmpty) {
          correspond &= c.libelle.toLowerCase().contains(libelle.toLowerCase());
        }

        if (numeroClient != null && numeroClient.isNotEmpty) {
          correspond &= c.numeroClient.contains(numeroClient);
        }

        if (nomClient != null && nomClient.isNotEmpty) {
          correspond &=
              (c.nomClient?.toLowerCase().contains(nomClient.toLowerCase()) ??
                  false);
        }

        if (zoneLivraison != null && zoneLivraison.isNotEmpty) {
          correspond &= c.zoneLivraison == zoneLivraison;
        }

        if (statut != null) {
          correspond &= c.statut == statut;
        }

        if (dateDebut != null) {
          correspond &= c.dateAjout.isAfter(
            dateDebut.subtract(const Duration(days: 1)),
          );
        }

        if (dateFin != null) {
          correspond &= c.dateAjout.isBefore(
            dateFin.add(const Duration(days: 1)),
          );
        }

        return correspond;
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de la recherche des colis: $e');
      }
      return [];
    }
  }

  /// Obtient les dernières livraisons (pour le dashboard)
  Future<Map<String, dynamic>> obtenirDonneesTableauDeBord() async {
    try {
      final colis = await loadColis();
      final maintenant = DateTime.now();

      // Colis du jour
      final colisDuJour =
          colis
              .where(
                (c) =>
                    c.dateAjout.year == maintenant.year &&
                    c.dateAjout.month == maintenant.month &&
                    c.dateAjout.day == maintenant.day,
              )
              .toList();

      // Dernière date de livraison
      DateTime? derniereLivraison;
      if (colis.isNotEmpty) {
        colis.sort((a, b) => b.dateAjout.compareTo(a.dateAjout));
        derniereLivraison = colis.first.dateAjout;
      }

      // Montant total par service de livraison (par zone)
      final montantParZone = <String, double>{};
      for (final coli in colisDuJour) {
        montantParZone[coli.zoneLivraison] =
            (montantParZone[coli.zoneLivraison] ?? 0) + coli.resteAPayer;
      }

      // Coût total de livraison du jour
      final coutTotalLivraison = colisDuJour.fold<double>(
        0,
        (sum, c) => sum + c.fraisLivraison,
      );

      // Point journalier
      final totalResteAPayer = colisDuJour.fold<double>(
        0,
        (sum, c) => sum + c.resteAPayer,
      );
      final pointJournalier = totalResteAPayer - coutTotalLivraison;

      return {
        'derniereLivraison': derniereLivraison,
        'montantParZone': montantParZone,
        'coutTotalLivraison': coutTotalLivraison,
        'pointJournalier': pointJournalier,
        'nombreColisDuJour': colisDuJour.length,
        'totalResteAPayer': totalResteAPayer,
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint(
          'Erreur lors de la récupération des données du tableau de bord: $e',
        );
      }
      return {
        'derniereLivraison': null,
        'montantParZone': <String, double>{},
        'coutTotalLivraison': 0.0,
        'pointJournalier': 0.0,
        'nombreColisDuJour': 0,
        'totalResteAPayer': 0.0,
      };
    }
  }

  /// Exporte les données en format JSON pour sauvegarde
  Future<Map<String, dynamic>> exporterDonnees() async {
    try {
      final colis = await loadColis();
      return {
        'colis': colis.map((c) => c.toJson()).toList(),
        'dateExport': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de l\'export des données: $e');
      }
      throw Exception('Impossible d\'exporter les données');
    }
  }

  /// Importe les données depuis un format JSON
  Future<void> importerDonnees(Map<String, dynamic> donnees) async {
    try {
      final colisData = donnees['colis'] as List;
      final colis =
          colisData
              .map((data) => Colis.fromJson(data as Map<String, dynamic>))
              .toList();

      await saveColis(colis);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors de l\'import des données: $e');
      }
      throw Exception('Impossible d\'importer les données');
    }
  }

  /// Nettoie les anciennes données (garde seulement les X derniers mois)
  Future<void> nettoyerAnciennesDonnees({int moisAGarder = 6}) async {
    try {
      final colis = await loadColis();
      final dateLimit = DateTime.now().subtract(
        Duration(days: moisAGarder * 30),
      );

      final colisAGarder =
          colis.where((c) => c.dateAjout.isAfter(dateLimit)).toList();

      await saveColis(colisAGarder);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Erreur lors du nettoyage des données: $e');
      }
      throw Exception('Impossible de nettoyer les anciennes données');
    }
  }
}
