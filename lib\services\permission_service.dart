import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Vérifie et demande les permissions pour la caméra
  Future<bool> requestCameraPermission() async {
    try {
      if (kIsWeb) {
        return true; // Sur le web, les permissions sont gérées par le navigateur
      }
      final status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Erreur permission caméra: $e');
      return false;
    }
  }

  /// Vérifie et demande les permissions pour le microphone
  Future<bool> requestMicrophonePermission() async {
    try {
      if (kIsWeb) {
        return true; // Sur le web, les permissions sont gérées par le navigateur
      }
      final status = await Permission.microphone.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Erreur permission microphone: $e');
      return false;
    }
  }

  /// Vérifie et demande les permissions pour le stockage
  Future<bool> requestStoragePermission() async {
    try {
      // Sur Flutter Web, les permissions sont gérées par le navigateur
      if (kIsWeb) {
        return true; // Supposer que les permissions sont accordées sur le web
      }

      if (Platform.isAndroid) {
        // Pour Android 13+ (API 33+)
        if (await _isAndroid13OrHigher()) {
          final photos = await Permission.photos.request();
          final videos = await Permission.videos.request();
          final audio = await Permission.audio.request();

          return photos.isGranted && videos.isGranted && audio.isGranted;
        } else {
          // Pour Android 12 et inférieur
          final storage = await Permission.storage.request();
          return storage.isGranted;
        }
      } else if (Platform.isIOS) {
        final photos = await Permission.photos.request();
        return photos.isGranted;
      }
      return false;
    } catch (e) {
      debugPrint('Erreur permission stockage: $e');
      return false;
    }
  }

  /// Vérifie et demande les permissions pour les médias (images)
  Future<bool> requestPhotosPermission() async {
    try {
      if (kIsWeb) {
        return true;
      }
      if (Platform.isAndroid && await _isAndroid13OrHigher()) {
        final status = await Permission.photos.request();
        return status.isGranted;
      } else {
        return await requestStoragePermission();
      }
    } catch (e) {
      debugPrint('Erreur permission photos: $e');
      return false;
    }
  }

  /// Vérifie et demande les permissions pour les vidéos
  Future<bool> requestVideosPermission() async {
    try {
      if (kIsWeb) {
        return true;
      }
      if (Platform.isAndroid && await _isAndroid13OrHigher()) {
        final status = await Permission.videos.request();
        return status.isGranted;
      } else {
        return await requestStoragePermission();
      }
    } catch (e) {
      debugPrint('Erreur permission vidéos: $e');
      return false;
    }
  }

  /// Vérifie et demande les permissions pour l'audio
  Future<bool> requestAudioPermission() async {
    try {
      if (kIsWeb) {
        return true;
      }
      if (Platform.isAndroid && await _isAndroid13OrHigher()) {
        final status = await Permission.audio.request();
        return status.isGranted;
      } else {
        return await requestStoragePermission();
      }
    } catch (e) {
      debugPrint('Erreur permission audio: $e');
      return false;
    }
  }

  /// Demande toutes les permissions nécessaires pour WhatsApp
  Future<Map<String, bool>> requestAllWhatsAppPermissions() async {
    final results = <String, bool>{};

    results['camera'] = await requestCameraPermission();
    results['microphone'] = await requestMicrophonePermission();
    results['storage'] = await requestStoragePermission();
    results['photos'] = await requestPhotosPermission();
    results['videos'] = await requestVideosPermission();
    results['audio'] = await requestAudioPermission();

    return results;
  }

  /// Vérifie le statut actuel des permissions
  Future<Map<String, PermissionStatus>> checkAllPermissions() async {
    final results = <String, PermissionStatus>{};

    if (kIsWeb) {
      // Sur le web, simuler des permissions accordées
      results['camera'] = PermissionStatus.granted;
      results['microphone'] = PermissionStatus.granted;
      results['storage'] = PermissionStatus.granted;
      results['photos'] = PermissionStatus.granted;
      results['videos'] = PermissionStatus.granted;
      results['audio'] = PermissionStatus.granted;
      return results;
    }

    results['camera'] = await Permission.camera.status;
    results['microphone'] = await Permission.microphone.status;

    if (Platform.isAndroid && await _isAndroid13OrHigher()) {
      results['photos'] = await Permission.photos.status;
      results['videos'] = await Permission.videos.status;
      results['audio'] = await Permission.audio.status;
    } else {
      results['storage'] = await Permission.storage.status;
      if (Platform.isIOS) {
        results['photos'] = await Permission.photos.status;
      }
    }

    return results;
  }

  /// Ouvre les paramètres de l'application
  Future<bool> openAppSettings() async {
    return await openAppSettings();
  }

  /// Affiche un dialogue d'explication des permissions
  Future<bool?> showPermissionDialog(
    BuildContext context, {
    required String title,
    required String message,
    required String permission,
  }) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  _getPermissionIcon(permission),
                  color: Colors.orange,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(child: Text(title)),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(message),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue.shade700,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Cette permission est nécessaire pour envoyer des médias via WhatsApp.',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Autoriser'),
              ),
            ],
          ),
    );
  }

  /// Vérifie si Android 13 ou supérieur
  Future<bool> _isAndroid13OrHigher() async {
    if (kIsWeb) return false;
    if (!Platform.isAndroid) return false;

    try {
      // Cette méthode peut être améliorée avec device_info_plus
      return true; // Supposons Android 13+ pour la sécurité
    } catch (e) {
      return false;
    }
  }

  /// Retourne l'icône appropriée pour chaque permission
  IconData _getPermissionIcon(String permission) {
    switch (permission.toLowerCase()) {
      case 'camera':
        return Icons.camera_alt;
      case 'microphone':
        return Icons.mic;
      case 'storage':
      case 'photos':
        return Icons.photo_library;
      case 'videos':
        return Icons.videocam;
      case 'audio':
        return Icons.audiotrack;
      default:
        return Icons.security;
    }
  }
}
