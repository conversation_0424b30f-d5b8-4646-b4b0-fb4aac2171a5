# Configuration Firebase

## <PERSON><PERSON>requis

Pour que l'application fonctionne correctement, vous devez configurer Firebase avec votre propre projet.

## Étapes de Configuration

### 1. Créer un Projet Firebase

1. Allez sur [Firebase Console](https://console.firebase.google.com/)
2. Cliquez sur "Ajouter un projet"
3. Suivez les étapes pour créer votre projet
4. Activez les services suivants :
   - **Authentication** (Email/Password)
   - **Realtime Database**
   - **Hosting** (optionnel)

### 2. Configuration Web

1. Dans votre projet Firebase, allez dans "Paramètres du projet"
2. Faites défiler jusqu'à "Vos applications"
3. Cliquez sur l'icône Web (</>) pour ajouter une application web
4. Donnez un nom à votre application
5. Copiez la configuration qui s'affiche

### 3. Mettre à Jour la Configuration

Remplacez le contenu du fichier `web/firebase-config.js` avec votre vraie configuration :

```javascript
const firebaseConfig = {
  apiKey: "votre-api-key",
  authDomain: "votre-projet.firebaseapp.com",
  databaseURL: "https://votre-projet-default-rtdb.firebaseio.com",
  projectId: "votre-projet-id",
  storageBucket: "votre-projet.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

### 4. Configuration Authentication

1. Dans Firebase Console, allez dans "Authentication"
2. Cliquez sur "Commencer"
3. Allez dans l'onglet "Sign-in method"
4. Activez "E-mail/Mot de passe"

### 5. Configuration Realtime Database

1. Dans Firebase Console, allez dans "Realtime Database"
2. Cliquez sur "Créer une base de données"
3. Choisissez un emplacement
4. Commencez en mode test (vous pourrez modifier les règles plus tard)

### 6. Règles de Sécurité (Recommandées)

Pour la Realtime Database, utilisez ces règles de base :

```json
{
  "rules": {
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid || root.child('users').child(auth.uid).child('role').val() === 'admin'",
        ".write": "$uid === auth.uid || root.child('users').child(auth.uid).child('role').val() === 'admin'"
      }
    },
    "colis": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    "products": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    "invoices": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    "tasks": {
      ".read": "auth != null",
      ".write": "auth != null"
    }
  }
}
```

## Test de la Configuration

1. Redémarrez l'application Flutter
2. Vérifiez les logs de la console pour voir si Firebase s'initialise correctement
3. Essayez de vous connecter avec les identifiants administrateur :
   - Email: `<EMAIL>`
   - Mot de passe: `Moiwallyd-007`

## Dépannage

### Erreur "FirebaseOptions cannot be null"
- Vérifiez que le fichier `firebase-config.js` est correctement configuré
- Assurez-vous que les scripts Firebase sont chargés dans `index.html`

### Erreur "No Firebase App '[DEFAULT]' has been created"
- Vérifiez que `firebase.initializeApp(firebaseConfig)` est appelé
- Vérifiez la console du navigateur pour les erreurs JavaScript

### Problèmes d'Authentication
- Vérifiez que l'authentication Email/Password est activée
- Vérifiez les règles de sécurité de la base de données

## Configuration pour le Développement

Pour le développement local, vous pouvez utiliser les émulateurs Firebase :

```bash
npm install -g firebase-tools
firebase login
firebase init emulators
firebase emulators:start
```

## Configuration pour la Production

1. Mettez à jour les règles de sécurité pour être plus restrictives
2. Configurez les domaines autorisés dans Firebase Console
3. Activez les quotas et la surveillance
4. Configurez les sauvegardes automatiques

## Support

Pour plus d'informations, consultez la [documentation Firebase](https://firebase.google.com/docs).