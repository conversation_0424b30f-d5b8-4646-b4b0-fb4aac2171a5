import 'package:flutter/material.dart';
import 'dart:math';

class CalculatorPage extends StatefulWidget {
  const CalculatorPage({super.key});

  @override
  State<CalculatorPage> createState() => _CalculatorPageState();
}

class _CalculatorPageState extends State<CalculatorPage> {
  String _display = '0';
  String _previousValue = '';
  String _operation = '';
  bool _waitingForOperand = false;
  final List<String> _history = [];

  void _inputDigit(String digit) {
    setState(() {
      if (_waitingForOperand) {
        _display = digit;
        _waitingForOperand = false;
      } else {
        _display = _display == '0' ? digit : _display + digit;
      }
    });
  }

  void _inputDecimal() {
    setState(() {
      if (_waitingForOperand) {
        _display = '0.';
        _waitingForOperand = false;
      } else if (!_display.contains('.')) {
        _display += '.';
      }
    });
  }

  void _clear() {
    setState(() {
      _display = '0';
      _previousValue = '';
      _operation = '';
      _waitingForOperand = false;
    });
  }

  void _clearAll() {
    setState(() {
      _display = '0';
      _previousValue = '';
      _operation = '';
      _waitingForOperand = false;
      _history.clear();
    });
  }

  void _performOperation(String nextOperation) {
    final inputValue = double.tryParse(_display) ?? 0;

    if (_previousValue.isEmpty) {
      _previousValue = _display;
    } else if (_operation.isNotEmpty) {
      final previousValue = double.tryParse(_previousValue) ?? 0;
      final result = _calculate(previousValue, inputValue, _operation);

      setState(() {
        _display = _formatResult(result);
        _previousValue = _display;
        _addToHistory('$previousValue $_operation $inputValue = $result');
      });
    }

    setState(() {
      _waitingForOperand = true;
      _operation = nextOperation;
    });
  }

  double _calculate(
    double firstOperand,
    double secondOperand,
    String operation,
  ) {
    switch (operation) {
      case '+':
        return firstOperand + secondOperand;
      case '-':
        return firstOperand - secondOperand;
      case '×':
        return firstOperand * secondOperand;
      case '÷':
        return secondOperand != 0 ? firstOperand / secondOperand : 0;
      case '%':
        return firstOperand % secondOperand;
      default:
        return secondOperand;
    }
  }

  String _formatResult(double result) {
    if (result == result.toInt()) {
      return result.toInt().toString();
    } else {
      return result
          .toStringAsFixed(8)
          .replaceAll(RegExp(r'0*$'), '')
          .replaceAll(RegExp(r'\.$'), '');
    }
  }

  void _addToHistory(String calculation) {
    setState(() {
      _history.insert(0, calculation);
      if (_history.length > 50) {
        _history.removeLast();
      }
    });
  }

  void _equals() {
    if (_operation.isNotEmpty && _previousValue.isNotEmpty) {
      final inputValue = double.tryParse(_display) ?? 0;
      final previousValue = double.tryParse(_previousValue) ?? 0;
      final result = _calculate(previousValue, inputValue, _operation);

      setState(() {
        _addToHistory('$previousValue $_operation $inputValue = $result');
        _display = _formatResult(result);
        _previousValue = '';
        _operation = '';
        _waitingForOperand = true;
      });
    }
  }

  void _backspace() {
    setState(() {
      if (_display.length > 1) {
        _display = _display.substring(0, _display.length - 1);
      } else {
        _display = '0';
      }
    });
  }

  void _squareRoot() {
    final value = double.tryParse(_display) ?? 0;
    if (value >= 0) {
      final result = sqrt(value);
      setState(() {
        _addToHistory('√$value = $result');
        _display = _formatResult(result);
        _waitingForOperand = true;
      });
    }
  }

  void _square() {
    final value = double.tryParse(_display) ?? 0;
    final result = value * value;
    setState(() {
      _addToHistory('$value² = $result');
      _display = _formatResult(result);
      _waitingForOperand = true;
    });
  }

  Widget _buildButton({
    required String text,
    required VoidCallback onPressed,
    Color? color,
    Color? textColor,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: Container(
        margin: const EdgeInsets.all(4),
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: color ?? Colors.grey[200],
            foregroundColor: textColor ?? Colors.black,
            padding: const EdgeInsets.all(20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
          child: Text(
            text,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calculatrice'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              showDialog(
                context: context,
                builder:
                    (context) => AlertDialog(
                      title: const Text('Historique'),
                      content: SizedBox(
                        width: double.maxFinite,
                        height: 300,
                        child:
                            _history.isEmpty
                                ? const Center(
                                  child: Text(
                                    'Aucun calcul dans l\'historique',
                                  ),
                                )
                                : ListView.builder(
                                  itemCount: _history.length,
                                  itemBuilder: (context, index) {
                                    return ListTile(
                                      title: Text(
                                        _history[index],
                                        style: const TextStyle(
                                          fontFamily: 'monospace',
                                        ),
                                      ),
                                      onTap: () {
                                        Navigator.of(context).pop();
                                      },
                                    );
                                  },
                                ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () {
                            setState(() {
                              _history.clear();
                            });
                            Navigator.of(context).pop();
                          },
                          child: const Text('Effacer'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Fermer'),
                        ),
                      ],
                    ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Affichage
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (_operation.isNotEmpty && _previousValue.isNotEmpty)
                    Text(
                      '$_previousValue $_operation',
                      style: TextStyle(fontSize: 20, color: Colors.grey[600]),
                    ),
                  const SizedBox(height: 8),
                  Text(
                    _display,
                    style: const TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.w300,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          // Boutons
          Expanded(
            flex: 5,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  // Première rangée
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton(
                          text: 'AC',
                          onPressed: _clearAll,
                          color: Colors.red[400],
                          textColor: Colors.white,
                        ),
                        _buildButton(
                          text: 'C',
                          onPressed: _clear,
                          color: Colors.orange[400],
                          textColor: Colors.white,
                        ),
                        _buildButton(
                          text: '⌫',
                          onPressed: _backspace,
                          color: Colors.orange[400],
                          textColor: Colors.white,
                        ),
                        _buildButton(
                          text: '÷',
                          onPressed: () => _performOperation('÷'),
                          color: Colors.blue[400],
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                  // Deuxième rangée
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton(
                          text: '√',
                          onPressed: _squareRoot,
                          color: Colors.green[400],
                          textColor: Colors.white,
                        ),
                        _buildButton(
                          text: 'x²',
                          onPressed: _square,
                          color: Colors.green[400],
                          textColor: Colors.white,
                        ),
                        _buildButton(
                          text: '%',
                          onPressed: () => _performOperation('%'),
                          color: Colors.blue[400],
                          textColor: Colors.white,
                        ),
                        _buildButton(
                          text: '×',
                          onPressed: () => _performOperation('×'),
                          color: Colors.blue[400],
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                  // Troisième rangée
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton(
                          text: '7',
                          onPressed: () => _inputDigit('7'),
                        ),
                        _buildButton(
                          text: '8',
                          onPressed: () => _inputDigit('8'),
                        ),
                        _buildButton(
                          text: '9',
                          onPressed: () => _inputDigit('9'),
                        ),
                        _buildButton(
                          text: '-',
                          onPressed: () => _performOperation('-'),
                          color: Colors.blue[400],
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                  // Quatrième rangée
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton(
                          text: '4',
                          onPressed: () => _inputDigit('4'),
                        ),
                        _buildButton(
                          text: '5',
                          onPressed: () => _inputDigit('5'),
                        ),
                        _buildButton(
                          text: '6',
                          onPressed: () => _inputDigit('6'),
                        ),
                        _buildButton(
                          text: '+',
                          onPressed: () => _performOperation('+'),
                          color: Colors.blue[400],
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                  // Cinquième rangée
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton(
                          text: '1',
                          onPressed: () => _inputDigit('1'),
                        ),
                        _buildButton(
                          text: '2',
                          onPressed: () => _inputDigit('2'),
                        ),
                        _buildButton(
                          text: '3',
                          onPressed: () => _inputDigit('3'),
                        ),
                        _buildButton(
                          text: '=',
                          onPressed: _equals,
                          color: Colors.green[500],
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                  // Sixième rangée
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton(
                          text: '0',
                          onPressed: () => _inputDigit('0'),
                          flex: 2,
                        ),
                        _buildButton(text: '.', onPressed: _inputDecimal),
                        _buildButton(
                          text: '=',
                          onPressed: _equals,
                          color: Colors.green[500],
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
