// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCNUNgGq-veTvbCeiLVU538IhhIK6oUmR8',
    appId: '1:540211492967:web:ced243fbffeaad2f877cc2',
    messagingSenderId: '540211492967',
    projectId: 'general-hcp-crm',
    authDomain: 'general-hcp-crm.firebaseapp.com',
    storageBucket: 'general-hcp-crm.firebasestorage.app',
    measurementId: 'G-35C7PNV6KX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA32ttr9phlaZtW1vQSFZInmtzJm5gCRiI',
    appId: '1:540211492967:android:a3bc96a208bb8ee2877cc2',
    messagingSenderId: '540211492967',
    projectId: 'general-hcp-crm',
    storageBucket: 'general-hcp-crm.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDelkJ8_9Yr6EzuGi7gPghAOyVJdajrTyc',
    appId: '1:540211492967:ios:8d1e66b6b9f6498a877cc2',
    messagingSenderId: '540211492967',
    projectId: 'general-hcp-crm',
    storageBucket: 'general-hcp-crm.firebasestorage.app',
    iosBundleId: 'com.example.generateurFacture',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDelkJ8_9Yr6EzuGi7gPghAOyVJdajrTyc',
    appId: '1:540211492967:ios:19a42be40ea867c9877cc2',
    messagingSenderId: '540211492967',
    projectId: 'general-hcp-crm',
    storageBucket: 'general-hcp-crm.firebasestorage.app',
    iosBundleId: 'com.example.generalHcpCrm',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCNUNgGq-veTvbCeiLVU538IhhIK6oUmR8',
    appId: '1:540211492967:web:47b17f21051e2a00877cc2',
    messagingSenderId: '540211492967',
    projectId: 'general-hcp-crm',
    authDomain: 'general-hcp-crm.firebaseapp.com',
    storageBucket: 'general-hcp-crm.firebasestorage.app',
    measurementId: 'G-0PRV2GDDQ7',
  );
}
