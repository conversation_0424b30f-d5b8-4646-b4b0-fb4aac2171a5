import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/ai_models.dart';
import '../models/whatsapp_chat.dart';

class KnowledgeService {
  static final KnowledgeService _instance = KnowledgeService._internal();
  factory KnowledgeService() => _instance;
  KnowledgeService._internal();

  static const String _articlesKey = 'knowledge_articles';
  static const String _categoriesKey = 'knowledge_categories';

  List<KnowledgeArticle> _articles = [];
  List<String> _categories = [];

  /// Initialise le service de base de connaissances
  Future<void> initialize() async {
    await _loadArticles();
    await _loadCategories();

    // Créer des articles de démonstration si aucun n'existe
    if (_articles.isEmpty) {
      await _createDefaultArticles();
    }
  }

  /// Crée des articles de démonstration
  Future<void> _createDefaultArticles() async {
    final defaultArticles = [
      KnowledgeArticle(
        id: 'art_1',
        title: 'Politique de Retour et Remboursement',
        content: '''
**Politique de Retour HCP-DESIGN**

Nous acceptons les retours dans les 30 jours suivant l'achat pour tous nos produits électroniques.

**Conditions de retour :**
• Produit en état neuf avec emballage d'origine
• Facture d'achat obligatoire
• Aucun dommage physique ou liquide

**Processus de retour :**
1. Contactez notre service client
2. Obtenez un numéro de retour (RMA)
3. Renvoyez le produit avec le RMA
4. Remboursement sous 5-7 jours ouvrés

**Frais de retour :**
• Gratuit si défaut de fabrication
• À la charge du client si changement d'avis

Pour plus d'informations, contactez-nous au +33 1 23 45 67 89
        ''',
        tags: ['retour', 'remboursement', 'politique', 'garantie'],
        keywords: [
          'retour',
          'remboursement',
          'rma',
          'garantie',
          'défaut',
          'échange',
        ],
        category: 'Service Client',
        relevanceScore: 0.9,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),

      KnowledgeArticle(
        id: 'art_2',
        title: 'Guide d\'Achat iPhone 15',
        content: '''
**iPhone 15 - Guide Complet**

L'iPhone 15 est le dernier smartphone d'Apple avec des fonctionnalités révolutionnaires.

**Caractéristiques principales :**
• Écran Super Retina XDR 6.1 pouces
• Puce A17 Pro ultra-rapide
• Système photo 48 MP avec zoom 2x
• USB-C pour la première fois
• Autonomie jusqu'à 20h de lecture vidéo

**Modèles disponibles :**
• iPhone 15 (128GB) - 6000 FCFA
• iPhone 15 (256GB) - 7000 FCFA
• iPhone 15 (512GB) - 8500 FCFA

**Couleurs disponibles :**
• Noir, Bleu, Vert, Jaune, Rose

**Accessoires inclus :**
• Câble USB-C vers USB-C
• Documentation

**Garantie :** 2 ans constructeur + 1 an HCP-DESIGN
        ''',
        tags: ['iphone', 'apple', 'smartphone', 'guide', 'achat'],
        keywords: [
          'iphone',
          '15',
          'apple',
          'smartphone',
          'prix',
          'couleur',
          'garantie',
        ],
        category: 'Produits',
        relevanceScore: 0.95,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),

      KnowledgeArticle(
        id: 'art_3',
        title: 'Suivi de Commande',
        content: '''
**Comment suivre votre commande**

Vous pouvez suivre l'état de votre commande de plusieurs façons :

**1. Par SMS :**
Vous recevrez des SMS automatiques à chaque étape :
• Commande confirmée
• Commande préparée
• Commande expédiée
• Commande livrée

**2. Par Email :**
Un email de confirmation avec numéro de suivi vous est envoyé.

**3. Sur notre site :**
Utilisez votre numéro de commande sur notre page de suivi.

**4. Par WhatsApp :**
Envoyez-nous votre numéro de commande ici même !

**Délais de livraison :**
• Dakar : 24-48h
• Régions : 2-5 jours ouvrés
• International : 7-14 jours

**Problème avec votre commande ?**
Contactez-nous immédiatement avec votre numéro de commande.
        ''',
        tags: ['commande', 'suivi', 'livraison', 'tracking'],
        keywords: [
          'commande',
          'suivi',
          'livraison',
          'tracking',
          'délai',
          'sms',
          'email',
        ],
        category: 'Livraison',
        relevanceScore: 0.85,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      KnowledgeArticle(
        id: 'art_4',
        title: 'Support Technique - Problèmes Courants',
        content: '''
**Solutions aux Problèmes Techniques Courants**

**Smartphone qui ne s'allume pas :**
1. Vérifiez la charge de la batterie
2. Maintenez le bouton power 10 secondes
3. Essayez un autre chargeur
4. Si le problème persiste, contactez-nous

**Écran cassé :**
• Réparation possible sous garantie si défaut
• Devis gratuit pour casse accidentelle
• Délai de réparation : 3-5 jours

**Problème de réseau :**
1. Redémarrez l'appareil
2. Vérifiez les paramètres réseau
3. Contactez votre opérateur
4. Réinitialisation réseau si nécessaire

**Problème de batterie :**
• Autonomie réduite normale après 2 ans
• Remplacement possible
• Conseils d'optimisation disponibles

**Contact Support :**
• WhatsApp : Message direct ici
• Téléphone : +33 1 23 45 67 89
• Email : <EMAIL>
        ''',
        tags: ['support', 'technique', 'problème', 'réparation'],
        keywords: [
          'support',
          'technique',
          'problème',
          'réparation',
          'écran',
          'batterie',
          'réseau',
        ],
        category: 'Support Technique',
        relevanceScore: 0.8,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now(),
      ),

      KnowledgeArticle(
        id: 'art_5',
        title: 'Modes de Paiement Acceptés',
        content: '''
**Modes de Paiement HCP-DESIGN**

Nous acceptons plusieurs modes de paiement pour votre confort :

**Paiement en ligne :**
• Carte bancaire (Visa, Mastercard)
• Mobile Money (Orange Money, Wave, Free Money)
• Virement bancaire
• PayPal

**Paiement à la livraison :**
• Espèces (exact de préférence)
• Mobile Money
• Carte bancaire (avec TPE mobile)

**Paiement en magasin :**
• Espèces
• Carte bancaire
• Mobile Money
• Chèque (avec pièce d'identité)

**Sécurité :**
• Toutes les transactions sont sécurisées SSL
• Aucune donnée bancaire stockée
• Conformité PCI DSS

**Facilités de paiement :**
• Paiement en 3x sans frais (>50 000 FCFA)
• Réservation avec acompte de 30%

**Questions sur le paiement ?**
Notre équipe est disponible pour vous aider !
        ''',
        tags: ['paiement', 'carte', 'mobile money', 'sécurité'],
        keywords: [
          'paiement',
          'carte',
          'mobile',
          'money',
          'espèces',
          'sécurité',
          'facilité',
        ],
        category: 'Paiement',
        relevanceScore: 0.75,
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];

    _articles = defaultArticles;
    _categories = [
      'Service Client',
      'Produits',
      'Livraison',
      'Support Technique',
      'Paiement',
    ];

    await _saveArticles();
    await _saveCategories();
  }

  /// Recherche des articles pertinents
  List<KnowledgeArticle> searchArticles(String query, {int limit = 5}) {
    if (query.isEmpty) return [];

    final lowercaseQuery = query.toLowerCase();
    final words = lowercaseQuery.split(' ').where((w) => w.length > 2).toList();

    // Calculer le score de pertinence pour chaque article
    final scoredArticles =
        _articles
            .map((article) {
              double score = 0.0;

              // Score basé sur le titre
              for (final word in words) {
                if (article.title.toLowerCase().contains(word)) {
                  score += 3.0;
                }
              }

              // Score basé sur les mots-clés
              for (final keyword in article.keywords) {
                for (final word in words) {
                  if (keyword.toLowerCase().contains(word)) {
                    score += 2.0;
                  }
                }
              }

              // Score basé sur les tags
              for (final tag in article.tags) {
                for (final word in words) {
                  if (tag.toLowerCase().contains(word)) {
                    score += 1.5;
                  }
                }
              }

              // Score basé sur le contenu
              for (final word in words) {
                if (article.content.toLowerCase().contains(word)) {
                  score += 1.0;
                }
              }

              // Bonus pour la pertinence de l'article
              score *= article.relevanceScore;

              return MapEntry(article, score);
            })
            .where((entry) => entry.value > 0)
            .toList();

    // Trier par score décroissant
    scoredArticles.sort((a, b) => b.value.compareTo(a.value));

    return scoredArticles.take(limit).map((entry) => entry.key).toList();
  }

  /// Détecte l'intention du client et trouve les articles appropriés
  List<KnowledgeArticle> getArticlesForIntention(CustomerIntention intention) {
    switch (intention) {
      case CustomerIntention.achat:
        return _articles
            .where(
              (a) =>
                  a.category == 'Produits' ||
                  a.tags.contains('achat') ||
                  a.tags.contains('prix'),
            )
            .toList();

      case CustomerIntention.support:
        return _articles
            .where(
              (a) =>
                  a.category == 'Support Technique' ||
                  a.category == 'Service Client' ||
                  a.tags.contains('support') ||
                  a.tags.contains('problème'),
            )
            .toList();

      case CustomerIntention.information:
        return _articles
            .where(
              (a) =>
                  a.category == 'Livraison' ||
                  a.category == 'Paiement' ||
                  a.tags.contains('information'),
            )
            .toList();

      case CustomerIntention.unknown:
        return _articles.take(3).toList();
    }
  }

  /// Trouve la meilleure réponse pour une question
  String? findBestAnswer(String question) {
    final articles = searchArticles(question, limit: 1);
    if (articles.isNotEmpty) {
      final article = articles.first;
      // Retourner un extrait pertinent de l'article
      final lines = article.content.split('\n');
      final relevantLines = lines
          .where(
            (line) => line.toLowerCase().contains(
              question.toLowerCase().split(' ').first,
            ),
          )
          .take(3);

      if (relevantLines.isNotEmpty) {
        return relevantLines.join('\n');
      }

      // Sinon retourner le début de l'article
      return lines.take(3).join('\n');
    }
    return null;
  }

  /// Génère un message de bienvenue personnalisé
  String generateWelcomeMessage(String customerName) {
    final greetings = [
      'Bonjour $customerName ! 👋',
      'Salut $customerName ! 😊',
      'Bonsoir $customerName ! 🌙',
      'Hello $customerName ! ✨',
    ];

    final messages = [
      'Bienvenue chez HCP-DESIGN ! Comment puis-je vous aider aujourd\'hui ?',
      'Ravi de vous revoir ! Que puis-je faire pour vous ?',
      'Comment allez-vous ? En quoi puis-je vous être utile ?',
      'Que puis-je faire pour vous aujourd\'hui ?',
    ];

    final random = Random();
    final greeting = greetings[random.nextInt(greetings.length)];
    final message = messages[random.nextInt(messages.length)];

    return '$greeting\n\n$message\n\n💡 Je peux vous aider avec :\n• Informations produits\n• Suivi de commandes\n• Support technique\n• Questions générales';
  }

  /// Obtient tous les articles
  List<KnowledgeArticle> getAllArticles() {
    return List.from(_articles);
  }

  /// Obtient les articles par catégorie
  List<KnowledgeArticle> getArticlesByCategory(String category) {
    return _articles.where((a) => a.category == category).toList();
  }

  /// Obtient toutes les catégories
  List<String> getCategories() {
    return List.from(_categories);
  }

  /// Ajoute un nouvel article
  Future<void> addArticle(KnowledgeArticle article) async {
    _articles.add(article);

    // Ajouter la catégorie si elle n'existe pas
    if (!_categories.contains(article.category)) {
      _categories.add(article.category);
      await _saveCategories();
    }

    await _saveArticles();
  }

  /// Met à jour un article
  Future<void> updateArticle(KnowledgeArticle updatedArticle) async {
    final index = _articles.indexWhere((a) => a.id == updatedArticle.id);
    if (index != -1) {
      _articles[index] = updatedArticle;
      await _saveArticles();
    }
  }

  /// Supprime un article
  Future<void> deleteArticle(String articleId) async {
    _articles.removeWhere((a) => a.id == articleId);
    await _saveArticles();
  }

  /// Obtient les statistiques de la base de connaissances
  Map<String, dynamic> getStatistics() {
    final totalArticles = _articles.length;
    final categoriesCount = _categories.length;
    final avgRelevance =
        _articles.isEmpty
            ? 0.0
            : _articles.map((a) => a.relevanceScore).reduce((a, b) => a + b) /
                totalArticles;

    final categoryStats = <String, int>{};
    for (final article in _articles) {
      categoryStats[article.category] =
          (categoryStats[article.category] ?? 0) + 1;
    }

    return {
      'totalArticles': totalArticles,
      'categoriesCount': categoriesCount,
      'averageRelevance': avgRelevance,
      'categoryStats': categoryStats,
      'lastUpdated':
          _articles.isNotEmpty
              ? _articles
                  .map((a) => a.updatedAt)
                  .reduce((a, b) => a.isAfter(b) ? a : b)
              : DateTime.now(),
    };
  }

  // Méthodes de persistance
  Future<void> _loadArticles() async {
    final prefs = await SharedPreferences.getInstance();
    final articlesJson = prefs.getString(_articlesKey);
    if (articlesJson != null) {
      final articlesList = jsonDecode(articlesJson) as List;
      _articles =
          articlesList.map((a) => KnowledgeArticle.fromJson(a)).toList();
    }
  }

  Future<void> _saveArticles() async {
    final prefs = await SharedPreferences.getInstance();
    final articlesJson = jsonEncode(_articles.map((a) => a.toJson()).toList());
    await prefs.setString(_articlesKey, articlesJson);
  }

  Future<void> _loadCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = prefs.getString(_categoriesKey);
    if (categoriesJson != null) {
      _categories = List<String>.from(jsonDecode(categoriesJson));
    }
  }

  Future<void> _saveCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = jsonEncode(_categories);
    await prefs.setString(_categoriesKey, categoriesJson);
  }
}
