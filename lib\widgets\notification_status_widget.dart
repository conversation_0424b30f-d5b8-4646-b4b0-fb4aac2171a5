import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationStatusWidget extends StatefulWidget {
  const NotificationStatusWidget({super.key});

  @override
  State<NotificationStatusWidget> createState() => _NotificationStatusWidgetState();
}

class _NotificationStatusWidgetState extends State<NotificationStatusWidget>
    with SingleTickerProviderStateMixin {
  bool _stockNotificationsEnabled = true;
  bool _taskNotificationsEnabled = true;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _initializePulseAnimation();
  }

  void _initializePulseAnimation() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        _stockNotificationsEnabled = prefs.getBool('stock_notifications_enabled') ?? true;
        _taskNotificationsEnabled = prefs.getBool('task_notifications_enabled') ?? true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool anyNotificationEnabled = _stockNotificationsEnabled || _taskNotificationsEnabled;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: anyNotificationEnabled ? Colors.green[50] : Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: anyNotificationEnabled ? Colors.green[200]! : Colors.orange[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Icône animée
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: anyNotificationEnabled ? _pulseAnimation.value : 1.0,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: anyNotificationEnabled ? Colors.green[600] : Colors.orange[600],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    anyNotificationEnabled ? Icons.notifications_active : Icons.notifications_off,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          
          // Texte de statut
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  anyNotificationEnabled ? 'Notifications Actives' : 'Notifications Désactivées',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    color: anyNotificationEnabled ? Colors.green[800] : Colors.orange[800],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _buildStatusText(),
                  style: TextStyle(
                    fontSize: 10,
                    color: anyNotificationEnabled ? Colors.green[600] : Colors.orange[600],
                  ),
                ),
              ],
            ),
          ),
          
          // Indicateurs de statut
          Column(
            children: [
              _buildStatusIndicator(
                'Stock',
                _stockNotificationsEnabled,
                Icons.inventory,
              ),
              const SizedBox(height: 4),
              _buildStatusIndicator(
                'Tâches',
                _taskNotificationsEnabled,
                Icons.task,
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _buildStatusText() {
    if (_stockNotificationsEnabled && _taskNotificationsEnabled) {
      return 'Stock (5h) • Tâches (30min)';
    } else if (_stockNotificationsEnabled) {
      return 'Stock uniquement (5h)';
    } else if (_taskNotificationsEnabled) {
      return 'Tâches uniquement (30min)';
    } else {
      return 'Aucune notification active';
    }
  }

  Widget _buildStatusIndicator(String label, bool enabled, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: enabled ? Colors.green[100] : Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 10,
            color: enabled ? Colors.green[700] : Colors.grey[500],
          ),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 8,
              fontWeight: FontWeight.w500,
              color: enabled ? Colors.green[700] : Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}

// Widget pour afficher les statistiques de notifications récentes
class NotificationStatsWidget extends StatelessWidget {
  const NotificationStatsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.notifications_active, color: Colors.blue[700], size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Notifications Récentes',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildNotificationItem(
              'Stocks Faibles',
              'Dernière vérification: Il y a 2h',
              Icons.inventory,
              Colors.orange,
            ),
            const SizedBox(height: 8),
            _buildNotificationItem(
              'Tâches en Attente',
              'Dernière vérification: Il y a 15min',
              Icons.task,
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
