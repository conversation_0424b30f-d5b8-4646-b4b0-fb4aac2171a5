# Structure des QR Codes pour Mini Factures HCP DESIGN

## 📁 Organisation des dossiers

Cette structure de dossiers contient tous les QR codes nécessaires pour la génération automatique des mini factures 5x3 pouces.

### QR_Site/
**Contenu :** QR code redirigeant vers le site web officiel de HCP DESIGN
- **Format :** PNG ou SVG, noir et blanc uniquement
- **Usage :** Affiché dans l'en-tête de la mini facture
- **Objectif :** Promotion du site web de l'entreprise

### QR_WhatsApp/
**Contenu :** QR code ouvrant une discussion WhatsApp Business avec HCP DESIGN
- **Format :** PNG ou SVG, noir et blanc uniquement
- **Usage :** Affiché dans l'en-tête de la mini facture
- **Objectif :** Contact direct avec l'entreprise

### QR_Coques/
**Contenu :** QR codes uniques pour chaque modèle de coque personnalisée
- **Format :** PNG ou SVG, noir et blanc uniquement
- **Usage :** Identification et traçabilité des produits
- **Objectif :** Suivi des commandes et authentification

### QR_Paiement_Wave/
**Contenu :** QR code de paiement via Wave Mobile Money
- **Format :** PNG ou SVG, noir et blanc uniquement
- **Usage :** Section paiement de la mini facture
- **Objectif :** Faciliter les paiements mobiles

### QR_Paiement_Orange/
**Contenu :** QR code de paiement via Orange Money
- **Format :** PNG ou SVG, noir et blanc uniquement
- **Usage :** Section paiement de la mini facture
- **Objectif :** Faciliter les paiements mobiles

## 🎯 Utilisation dans l'application

### Génération automatique
Les QR codes sont automatiquement intégrés lors de la génération des mini factures via :
```dart
PDFService.generateAndDownloadMiniInvoice(
  nomClient: 'Nom du client',
  numeroClient: 'CLI-001',
  lieuLivraison: 'Adresse de livraison',
  resteAPayer: 15000.0,
);
```

### Format de la mini facture
- **Dimensions :** 5 x 3 pouces (12,7 x 7,6 cm)
- **Résolution :** Optimisée pour impression thermique
- **Couleurs :** Noir et blanc uniquement
- **Sections :**
  1. En-tête avec logo et QR codes (site + WhatsApp)
  2. Informations client
  3. Section paiement avec QR codes (Wave + Orange)

## 📋 Instructions de mise à jour

### Remplacement des QR codes
1. Générer le nouveau QR code en noir et blanc
2. Sauvegarder au format PNG ou SVG
3. Remplacer le fichier existant dans le dossier approprié
4. Respecter les noms de fichiers :
   - `qr_site.png/svg` pour le site web
   - `qr_whatsapp.png/svg` pour WhatsApp
   - `qr_wave.png/svg` pour Wave
   - `qr_orange.png/svg` pour Orange
   - Noms personnalisés pour les coques

### Ajout de nouveaux QR codes de coques
1. Créer le QR code unique pour la coque
2. Nommer le fichier avec l'identifiant de la coque
3. Placer dans le dossier `QR_Coques/`
4. Le système chargera automatiquement le QR code correspondant

## ⚠️ Contraintes techniques

- **Couleurs :** Strictement noir et blanc, aucune couleur
- **Résolution :** Minimum 100x100 pixels pour la lisibilité
- **Format :** PNG recommandé, SVG accepté
- **Taille :** Optimisée pour impression petit format
- **Contraste :** Maximum pour garantir la lecture par scanners

## 🔧 Maintenance

- Vérifier régulièrement la validité des liens dans les QR codes
- Tester la lisibilité après impression
- Mettre à jour les QR codes de paiement si les comptes changent
- Sauvegarder les QR codes dans un système de versioning