import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';
import '../services/inventory_service.dart';

class CreateProformaPage extends StatefulWidget {
  const CreateProformaPage({super.key});

  @override
  State<CreateProformaPage> createState() => _CreateProformaPageState();
}

class _CreateProformaPageState extends State<CreateProformaPage> {
  final _formKey = GlobalKey<FormState>();
  final _clientNameController = TextEditingController();
  final _clientNumberController = TextEditingController();
  final _clientAddressController = TextEditingController();
  final _clientEmailController = TextEditingController();
  final _productsController = TextEditingController();
  final _deliveryDetailsController = TextEditingController();
  final _discountController = TextEditingController();
  final _advanceController = TextEditingController();
  final _notesController = TextEditingController();
  final _footerNoteController = TextEditingController();
  final _companyRccmController = TextEditingController();
  final _companyTaxNumberController = TextEditingController();
  final _specialConditionsController = TextEditingController();
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  final InvoiceService _invoiceService = InvoiceService();
  final InventoryService _inventoryService = InventoryService.instance;

  final List<InvoiceItem> _items = [];
  String? _selectedDeliveryZone;
  String? _selectedLogoPath;
  DateTime? _validityDate;
  final List<String> _selectedPaymentMethods = [];

  double _deliveryPrice = 0;
  double _discount = 0;
  double _advance = 0;
  double _subtotal = 0;
  double _total = 0;

  // Modes de paiement disponibles
  final List<String> _availablePaymentMethods = [
    'Mobile Money',
    'Virement bancaire',
    'Espèces',
    'Chèque',
    'Orange Money',
    'MTN Money',
    'Moov Money',
    'Wave',
  ];

  @override
  void initState() {
    super.initState();
    _loadProducts();
    _addNewItem();
    _setDefaultValidityDate();
  }

  void _setDefaultValidityDate() {
    // Date de validité par défaut : 30 jours à partir d'aujourd'hui
    _validityDate = DateTime.now().add(const Duration(days: 30));
  }

  Future<void> _loadProducts() async {
    try {
      await _inventoryService.getProducts();
      // Produits chargés avec succès
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement des produits: $e')),
        );
      }
    }
  }

  void _addNewItem() {
    setState(() {
      _items.add(
        InvoiceItem(
          id: _invoiceService.generateItemId(),
          name: '',
          price: 0,
          quantity: 1,
          isCustom: false,
        ),
      );
    });
  }

  void _removeItem(int index) {
    if (_items.length > 1) {
      setState(() {
        _items.removeAt(index);
        _calculateTotals();
      });
    }
  }

  void _updateItem(int index, InvoiceItem updatedItem) {
    setState(() {
      _items[index] = updatedItem;
      _calculateTotals();
    });
  }

  void _calculateTotals() {
    double itemsTotal = 0;
    for (var item in _items) {
      if (item.name.isNotEmpty) {
        itemsTotal += item.price * item.quantity;
      }
    }

    setState(() {
      _subtotal = itemsTotal;
      _total = _subtotal + _deliveryPrice - _discount - _advance;
    });
  }

  Future<void> _selectValidityDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _validityDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('fr', 'FR'),
    );
    if (picked != null && picked != _validityDate) {
      setState(() {
        _validityDate = picked;
      });
    }
  }

  Future<void> _selectLogo() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null) {
        setState(() {
          _selectedLogoPath = result.files.single.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sélection du logo: $e')),
        );
      }
    }
  }

  void _showPaymentMethodsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Modes de paiement acceptés'),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _availablePaymentMethods.length,
                  itemBuilder: (context, index) {
                    final method = _availablePaymentMethods[index];
                    return CheckboxListTile(
                      title: Text(method),
                      value: _selectedPaymentMethods.contains(method),
                      onChanged: (bool? value) {
                        setDialogState(() {
                          if (value == true) {
                            _selectedPaymentMethods.add(method);
                          } else {
                            _selectedPaymentMethods.remove(method);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Fermer'),
                ),
              ],
            );
          },
        );
      },
    ).then((_) {
      setState(() {}); // Rafraîchir l'affichage
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouvelle Facture Proforma'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // En-tête avec mention PROFORMA
              Card(
                color: Colors.orange[50],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'FACTURE PROFORMA',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange[800],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Cette facture proforma ne vaut pas facture définitive',
                        style: TextStyle(
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                          color: Colors.orange[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Informations client
              _buildClientSection(),
              const SizedBox(height: 16),

              // Informations entreprise
              _buildCompanySection(),
              const SizedBox(height: 16),

              // Date de validité
              _buildValiditySection(),
              const SizedBox(height: 16),

              // Articles
              _buildItemsSection(),
              const SizedBox(height: 16),

              // Livraison et totaux
              _buildDeliveryAndTotalsSection(),
              const SizedBox(height: 16),

              // Modes de paiement
              _buildPaymentMethodsSection(),
              const SizedBox(height: 16),

              // Conditions spéciales et notes
              _buildNotesSection(),
              const SizedBox(height: 24),

              // Boutons d'action
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildClientSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations Client',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _clientNameController,
              decoration: const InputDecoration(
                labelText: 'Nom du client / Entreprise *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez saisir le nom du client';
                }
                return null;
              },
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _clientNumberController,
              decoration: const InputDecoration(
                labelText: 'Téléphone / WhatsApp *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez saisir le numéro de téléphone';
                }
                return null;
              },
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _clientAddressController,
              decoration: const InputDecoration(
                labelText: 'Adresse du client',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _clientEmailController,
              decoration: const InputDecoration(
                labelText: 'Email du client',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations Entreprise',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _companyRccmController,
                    decoration: const InputDecoration(
                      labelText: 'N° RCCM',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.business),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _companyTaxNumberController,
                    decoration: const InputDecoration(
                      labelText: 'N° Contribuable',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.receipt_long),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            OutlinedButton.icon(
              onPressed: _selectLogo,
              icon: const Icon(Icons.image),
              label: Text(
                _selectedLogoPath != null
                    ? 'Logo sélectionné'
                    : 'Sélectionner un logo',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildValiditySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Date de Validité',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: _selectValidityDate,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today),
                    const SizedBox(width: 12),
                    Text(
                      _validityDate != null
                          ? 'Valable jusqu\'au : ${DateFormat('dd/MM/yyyy').format(_validityDate!)}'
                          : 'Sélectionner une date de validité',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Articles',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
                IconButton(
                  onPressed: _addNewItem,
                  icon: const Icon(Icons.add_circle),
                  color: Colors.green,
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _productsController,
              decoration: const InputDecoration(
                labelText: 'Description générale de la commande',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            ..._items.asMap().entries.map((entry) {
              int index = entry.key;
              InvoiceItem item = entry.value;
              return _buildItemRow(index, item);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildItemRow(int index, InvoiceItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Row(
              children: [
                Text('Article ${index + 1}'),
                const Spacer(),
                if (_items.length > 1)
                  IconButton(
                    onPressed: () => _removeItem(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    iconSize: 20,
                  ),
              ],
            ),
            const SizedBox(height: 8),
            TextFormField(
              initialValue: item.name,
              decoration: const InputDecoration(
                labelText: 'Désignation',
                border: OutlineInputBorder(),
                isDense: true,
              ),
              onChanged: (value) {
                _updateItem(index, item.copyWith(name: value));
              },
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: item.quantity.toString(),
                    decoration: const InputDecoration(
                      labelText: 'Qté',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    onChanged: (value) {
                      final quantity = int.tryParse(value) ?? 1;
                      _updateItem(index, item.copyWith(quantity: quantity));
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    initialValue: item.price.toString(),
                    decoration: const InputDecoration(
                      labelText: 'Prix unitaire (FCFA)',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final price = double.tryParse(value) ?? 0;
                      _updateItem(index, item.copyWith(price: price));
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${_currencyFormat.format(item.price * item.quantity)} FCFA',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryAndTotalsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Livraison et Totaux',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedDeliveryZone,
              decoration: const InputDecoration(
                labelText: 'Zone de livraison',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.local_shipping),
              ),
              items:
                  DeliveryZones.availableZones.map((zone) {
                    return DropdownMenuItem(
                      value: zone,
                      child: Text(
                        '$zone (${_currencyFormat.format(DeliveryZones.getDeliveryPrice(zone))} FCFA)',
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedDeliveryZone = value;
                  _deliveryPrice = DeliveryZones.getDeliveryPrice(value ?? '');
                  _calculateTotals();
                });
              },
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _deliveryDetailsController,
              decoration: const InputDecoration(
                labelText: 'Détails de livraison',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.info),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            _buildTotalsDisplay(),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalsDisplay() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Sous-total:', style: TextStyle(fontSize: 16)),
            Text(
              '${_currencyFormat.format(_subtotal)} FCFA',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _discountController,
                decoration: const InputDecoration(
                  labelText: 'Remise (FCFA)',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() {
                    _discount = double.tryParse(value) ?? 0;
                    _calculateTotals();
                  });
                },
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TextFormField(
                controller: _advanceController,
                decoration: const InputDecoration(
                  labelText: 'Avance (FCFA)',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  setState(() {
                    _advance = double.tryParse(value) ?? 0;
                    _calculateTotals();
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Frais de livraison:', style: TextStyle(fontSize: 16)),
            Text(
              '${_currencyFormat.format(_deliveryPrice)} FCFA',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        const Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'TOTAL PROFORMA:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            Text(
              '${_currencyFormat.format(_total)} FCFA',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green[700],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentMethodsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Modes de Paiement Acceptés',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: _showPaymentMethodsDialog,
              icon: const Icon(Icons.payment),
              label: Text(
                _selectedPaymentMethods.isEmpty
                    ? 'Sélectionner les modes de paiement'
                    : '${_selectedPaymentMethods.length} mode(s) sélectionné(s)',
              ),
            ),
            if (_selectedPaymentMethods.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children:
                    _selectedPaymentMethods.map((method) {
                      return Chip(
                        label: Text(method),
                        backgroundColor: Colors.blue[50],
                      );
                    }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Conditions et Notes',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _specialConditionsController,
              decoration: const InputDecoration(
                labelText: 'Conditions spécifiques (délais, options, etc.)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.rule),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes additionnelles',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _footerNoteController,
              decoration: const InputDecoration(
                labelText: 'Note de pied de page',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.text_snippet),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.cancel),
            label: const Text('Annuler'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _saveProforma,
            icon: const Icon(Icons.save),
            label: const Text('Créer Proforma'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[700],
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveProforma() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Vérifier qu'au moins un article est rempli
    final validItems = _items.where((item) => item.name.isNotEmpty).toList();
    if (validItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez ajouter au moins un article')),
      );
      return;
    }

    // Vérifier la date de validité
    if (_validityDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner une date de validité'),
        ),
      );
      return;
    }

    try {
      final invoice = Invoice(
        id: '', // Sera généré par le service
        clientName: _clientNameController.text.trim(),
        clientNumber: _clientNumberController.text.trim(),
        clientAddress:
            _clientAddressController.text.trim().isEmpty
                ? null
                : _clientAddressController.text.trim(),
        clientEmail:
            _clientEmailController.text.trim().isEmpty
                ? null
                : _clientEmailController.text.trim(),
        products: _productsController.text.trim(),
        items: validItems,
        deliveryLocation: _selectedDeliveryZone ?? '',
        deliveryDetails:
            _deliveryDetailsController.text.trim().isEmpty
                ? null
                : _deliveryDetailsController.text.trim(),
        deliveryPrice: _deliveryPrice,
        discountAmount: _discount,
        advance: _advance,
        subtotal: _subtotal,
        total: _total,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        logoPath: _selectedLogoPath,
        footerNote:
            _footerNoteController.text.trim().isEmpty
                ? null
                : _footerNoteController.text.trim(),
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
        type: InvoiceType.proforma,
        validityDate: _validityDate,
        companyRccm:
            _companyRccmController.text.trim().isEmpty
                ? null
                : _companyRccmController.text.trim(),
        companyTaxNumber:
            _companyTaxNumberController.text.trim().isEmpty
                ? null
                : _companyTaxNumberController.text.trim(),
        paymentMethods:
            _selectedPaymentMethods.isEmpty ? null : _selectedPaymentMethods,
        specialConditions:
            _specialConditionsController.text.trim().isEmpty
                ? null
                : _specialConditionsController.text.trim(),
      );

      await _invoiceService.addInvoice(invoice);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Facture proforma créée avec succès')),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la création: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _clientNameController.dispose();
    _clientNumberController.dispose();
    _clientAddressController.dispose();
    _clientEmailController.dispose();
    _productsController.dispose();
    _deliveryDetailsController.dispose();
    _discountController.dispose();
    _advanceController.dispose();
    _notesController.dispose();
    _footerNoteController.dispose();
    _companyRccmController.dispose();
    _companyTaxNumberController.dispose();
    _specialConditionsController.dispose();
    super.dispose();
  }
}
