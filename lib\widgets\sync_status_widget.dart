import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../services/sync_service.dart';

class SyncStatusWidget extends StatefulWidget {
  final bool showControls;
  final bool compact;

  const SyncStatusWidget({
    super.key,
    this.showControls = true,
    this.compact = false,
  });

  @override
  State<SyncStatusWidget> createState() => _SyncStatusWidgetState();
}

class _SyncStatusWidgetState extends State<SyncStatusWidget> {
  final SyncService _syncService = SyncService.instance;
  Timer? _refreshTimer;
  Map<String, dynamic> _syncStatus = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSyncStatus();
    // Refresh status every 5 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _loadSyncStatus();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadSyncStatus() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final status = await _syncService.getSyncStatus();
      if (mounted) {
        setState(() {
          _syncStatus = status;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _syncNow() async {
    if (_syncStatus['isSyncing'] == true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _syncService.syncNow();
      await _loadSyncStatus();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _toggleAutoSync() async {
    final currentValue = _syncStatus['autoSyncEnabled'] ?? false;
    await _syncService.setAutoSyncEnabled(!currentValue);
    await _loadSyncStatus();
  }

  Future<void> _changeSyncInterval() async {
    final currentInterval = _syncStatus['syncInterval'] ?? 15;

    final result = await showDialog<int>(
      context: context,
      builder:
          (context) => _SyncIntervalDialog(currentInterval: currentInterval),
    );

    if (result != null) {
      await _syncService.setSyncInterval(result);
      await _loadSyncStatus();
    }
  }

  String _formatLastSyncTime() {
    final lastSyncTime = _syncStatus['lastSyncTime'];
    if (lastSyncTime == null) return 'Jamais';

    final dateTime = DateTime.parse(lastSyncTime);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inHours < 1) {
      return 'Il y a ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
    } else if (difference.inDays < 1) {
      return 'Il y a ${difference.inHours} heure${difference.inHours > 1 ? 's' : ''}';
    } else {
      final formatter = DateFormat('dd/MM/yyyy HH:mm');
      return formatter.format(dateTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isOnline = _syncStatus['isOnline'] ?? false;
    final isSyncing = _syncStatus['isSyncing'] ?? false;
    final pendingOperations = _syncStatus['pendingOperations'] ?? 0;
    final autoSyncEnabled = _syncStatus['autoSyncEnabled'] ?? false;
    final syncInterval = _syncStatus['syncInterval'] ?? 15;
    final initialMigrationDone = _syncStatus['initialMigrationDone'] ?? false;

    if (widget.compact) {
      return _buildCompactWidget(isOnline, isSyncing, pendingOperations);
    }

    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  isOnline ? Icons.cloud_done : Icons.cloud_off,
                  color: isOnline ? Colors.green : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  'Firebase: ${isOnline ? "Connecté" : "Déconnecté"}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                if (widget.showControls)
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: _isLoading ? null : _syncNow,
                    tooltip: 'Synchroniser maintenant',
                  ),
              ],
            ),
            const Divider(),
            if (isSyncing)
              const LinearProgressIndicator()
            else if (pendingOperations > 0)
              LinearProgressIndicator(value: 0.5),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Dernière synchronisation: ${_formatLastSyncTime()}',
                  ),
                ),
              ],
            ),
            if (pendingOperations > 0)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    const Icon(Icons.pending_actions, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      '$pendingOperations opération${pendingOperations > 1 ? 's' : ''} en attente',
                    ),
                  ],
                ),
              ),
            if (!initialMigrationDone)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.orange, size: 16),
                    const SizedBox(width: 8),
                    const Text('Migration initiale non effectuée'),
                  ],
                ),
              ),
            if (widget.showControls) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Switch(
                    value: autoSyncEnabled,
                    onChanged: (value) => _toggleAutoSync(),
                  ),
                  const SizedBox(width: 8),
                  const Text('Synchronisation automatique'),
                  const Spacer(),
                  if (autoSyncEnabled)
                    TextButton(
                      onPressed: _changeSyncInterval,
                      child: Text(
                        'Toutes les $syncInterval minute${syncInterval > 1 ? 's' : ''}',
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _isLoading || isSyncing ? null : _syncNow,
                icon: const Icon(Icons.sync),
                label: Text(
                  isSyncing
                      ? 'Synchronisation en cours...'
                      : 'Synchroniser maintenant',
                ),
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 40),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompactWidget(
    bool isOnline,
    bool isSyncing,
    int pendingOperations,
  ) {
    return InkWell(
      onTap: _syncNow,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isOnline ? Icons.cloud_done : Icons.cloud_off,
              color: isOnline ? Colors.green : Colors.grey,
              size: 16,
            ),
            const SizedBox(width: 4),
            if (isSyncing)
              const SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else if (pendingOperations > 0)
              Badge(
                label: Text('$pendingOperations'),
                child: const Icon(Icons.pending_actions, size: 16),
              )
            else
              const Icon(Icons.sync, size: 16),
          ],
        ),
      ),
    );
  }
}

class _SyncIntervalDialog extends StatefulWidget {
  final int currentInterval;

  const _SyncIntervalDialog({required this.currentInterval});

  @override
  State<_SyncIntervalDialog> createState() => _SyncIntervalDialogState();
}

class _SyncIntervalDialogState extends State<_SyncIntervalDialog> {
  late int _interval;

  @override
  void initState() {
    super.initState();
    _interval = widget.currentInterval;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Intervalle de synchronisation'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Choisissez l\'intervalle de synchronisation automatique (en minutes)',
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.remove),
                onPressed:
                    _interval > 1 ? () => setState(() => _interval--) : null,
              ),
              Text(
                '$_interval',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: () => setState(() => _interval++),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Synchronisation toutes les $_interval minute${_interval > 1 ? 's' : ''}',
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(_interval),
          child: const Text('Confirmer'),
        ),
      ],
    );
  }
}
