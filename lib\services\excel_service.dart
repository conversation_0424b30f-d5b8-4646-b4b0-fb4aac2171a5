import 'dart:typed_data';
import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import '../models/product.dart';
import '../models/category.dart' as app_models;
import 'inventory_service.dart';

class ExcelService {
  final InventoryService _inventoryService = InventoryService.instance;

  /// Exporte les produits vers un fichier Excel
  Future<Uint8List> exportProductsToExcel(List<Product> products) async {
    var excel = Excel.createExcel();
    Sheet sheetObject = excel['Produits'];

    // Supprimer la feuille par défaut si elle existe
    if (excel.sheets.containsKey('Sheet1')) {
      excel.delete('Sheet1');
    }

    // Récupérer les catégories pour les noms
    List<app_models.Category> categories =
        await _inventoryService.getCategories();
    Map<String, String> categoryMap = {
      for (var cat in categories) cat.id: cat.name,
    };

    // En-têtes
    List<String> headers = [
      'ID',
      'Nom',
      'Prix',
      'Quantité',
      'Description',
      'ID Catégorie',
      'Nom Catégorie',
      'URL Image',
    ];

    // Ajouter les en-têtes
    for (int i = 0; i < headers.length; i++) {
      var cell = sheetObject.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.blue,
        fontColorHex: ExcelColor.white,
      );
    }

    // Ajouter les données des produits
    for (int rowIndex = 0; rowIndex < products.length; rowIndex++) {
      Product product = products[rowIndex];
      List<dynamic> rowData = [
        product.id,
        product.name,
        product.price,
        product.quantity,
        product.description,
        product.categoryId,
        categoryMap[product.categoryId] ?? 'N/A',
        product.imageUrl ?? '',
      ];

      for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
        var cell = sheetObject.cell(
          CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 1,
          ),
        );

        var value = rowData[colIndex];
        if (value is String) {
          cell.value = TextCellValue(value);
        } else if (value is num) {
          cell.value = DoubleCellValue(value.toDouble());
        } else {
          cell.value = TextCellValue(value.toString());
        }
      }
    }

    // Ajuster la largeur des colonnes
    for (int i = 0; i < headers.length; i++) {
      sheetObject.setColumnWidth(i, 15.0);
    }

    // Encoder le fichier Excel
    List<int>? encodedBytes = excel.encode();
    if (encodedBytes == null) {
      throw Exception('Erreur lors de l\'encodage du fichier Excel');
    }
    return Uint8List.fromList(encodedBytes);
  }

  /// Importe les produits depuis un fichier Excel
  Future<List<Product>> importProductsFromExcel(Uint8List bytes) async {
    var excel = Excel.decodeBytes(bytes);
    List<Product> importedProducts = [];
    List<app_models.Category> existingCategories =
        await _inventoryService.getCategories();

    // Prendre la première feuille disponible
    if (excel.sheets.isEmpty) {
      throw Exception('Aucune feuille trouvée dans le fichier Excel.');
    }
    String sheetName = excel.sheets.keys.first;

    Sheet sheet = excel.sheets[sheetName]!;

    if (sheet.maxRows < 2) {
      throw Exception(
        'Le fichier Excel doit contenir au moins une ligne d\'en-tête et une ligne de données.',
      );
    }

    // Valider les en-têtes (ligne 0)
    List<String> expectedHeaders = [
      'nom',
      'prix',
      'quantité',
      'description',
      'nom catégorie',
      'url image',
    ];

    List<String> actualHeaders = [];
    for (int col = 0; col < expectedHeaders.length; col++) {
      var cell = sheet.cell(
        CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0),
      );
      actualHeaders.add(cell.value?.toString().toLowerCase().trim() ?? '');
    }

    // Normaliser les en-têtes pour accepter différents formats
    Map<String, String> headerMapping = {
      'nom': 'nom',
      'name': 'nom',
      'prix': 'prix',
      'price': 'prix',
      'quantité': 'quantité',
      'quantite': 'quantité',
      'quantity': 'quantité',
      'description': 'description',
      'nom catégorie': 'nom catégorie',
      'nom categorie': 'nom catégorie',
      'category': 'nom catégorie',
      'catégorie': 'nom catégorie',
      'categorie': 'nom catégorie',
      'url image': 'url image',
      'image url': 'url image',
      'image': 'url image',
    };

    // Vérifier que les en-têtes correspondent (avec flexibilité)
    for (int i = 0; i < expectedHeaders.length; i++) {
      if (i >= actualHeaders.length) {
        throw Exception(
          'Format d\'en-tête Excel invalide. Colonnes manquantes. Attendu: ${expectedHeaders.join(", ")}',
        );
      }

      String actualHeader = actualHeaders[i];
      String expectedHeader = expectedHeaders[i];

      // Vérifier si l'en-tête actuel correspond à l'attendu (directement ou via mapping)
      bool isValid =
          actualHeader == expectedHeader ||
          headerMapping[actualHeader] == expectedHeader;

      if (!isValid) {
        throw Exception(
          'Format d\'en-tête Excel invalide à la colonne ${i + 1}. '
          'Trouvé: "$actualHeader", Attendu: "$expectedHeader" ou équivalent. '
          'Format complet attendu: ${expectedHeaders.join(", ")}',
        );
      }
    }

    // Traiter les données (à partir de la ligne 1)
    for (int rowIndex = 1; rowIndex < sheet.maxRows; rowIndex++) {
      try {
        // Lire les valeurs de la ligne
        String productName = _getCellValue(sheet, rowIndex, 0).trim();
        String priceStr = _getCellValue(sheet, rowIndex, 1).trim();
        String quantityStr = _getCellValue(sheet, rowIndex, 2).trim();
        String productDescription = _getCellValue(sheet, rowIndex, 3).trim();
        String categoryName = _getCellValue(sheet, rowIndex, 4).trim();
        String imageUrl = _getCellValue(sheet, rowIndex, 5).trim();

        // Valider les données obligatoires
        if (productName.isEmpty || categoryName.isEmpty) {
          debugPrint('Ligne $rowIndex ignorée: nom ou catégorie manquant');
          continue;
        }

        double? productPrice = double.tryParse(priceStr);
        int? productQuantity = int.tryParse(quantityStr);

        if (productPrice == null || productQuantity == null) {
          debugPrint('Ligne $rowIndex ignorée: prix ou quantité invalide');
          continue;
        }

        // Trouver la catégorie
        app_models.Category? category;
        try {
          category = existingCategories.firstWhere(
            (cat) => cat.name.toLowerCase() == categoryName.toLowerCase(),
          );
        } catch (e) {
          debugPrint(
            'Catégorie "$categoryName" non trouvée pour le produit "$productName". Produit ignoré.',
          );
          continue;
        }

        // Créer le produit
        importedProducts.add(
          Product(
            id: '', // Sera généré par le service
            name: productName,
            price: productPrice,
            quantity: productQuantity,
            description: productDescription,
            categoryId: category.id,
            imageUrl: imageUrl.isNotEmpty ? imageUrl : null,
          ),
        );
      } catch (e) {
        debugPrint('Erreur lors du traitement de la ligne $rowIndex: $e');
        continue;
      }
    }

    return importedProducts;
  }

  /// Génère un modèle Excel pour l'importation
  Uint8List getExcelTemplate() {
    var excel = Excel.createExcel();
    Sheet sheetObject = excel['Modèle Produits'];

    // Supprimer la feuille par défaut
    if (excel.sheets.containsKey('Sheet1')) {
      excel.delete('Sheet1');
    }

    // En-têtes pour l'import (format standard avec majuscules)
    List<String> headers = [
      'Nom',
      'Prix',
      'Quantité',
      'Description',
      'Nom Catégorie',
      'URL Image',
    ];

    // Ajouter les en-têtes
    for (int i = 0; i < headers.length; i++) {
      var cell = sheetObject.cell(
        CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0),
      );
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.blue,
        fontColorHex: ExcelColor.white,
      );
    }

    // Ligne d'exemple
    List<dynamic> exampleRow = [
      'iPhone 15',
      6000,
      10,
      'Smartphone Apple dernière génération',
      'Électronique',
      'https://example.com/iphone15.jpg',
    ];

    for (int colIndex = 0; colIndex < exampleRow.length; colIndex++) {
      var cell = sheetObject.cell(
        CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: 1),
      );

      var value = exampleRow[colIndex];
      if (value is String) {
        cell.value = TextCellValue(value);
      } else if (value is num) {
        cell.value = DoubleCellValue(value.toDouble());
      } else {
        cell.value = TextCellValue(value.toString());
      }
    }

    // Ajuster la largeur des colonnes
    for (int i = 0; i < headers.length; i++) {
      sheetObject.setColumnWidth(i, 20.0);
    }

    // Encoder le fichier Excel template
    List<int>? encodedBytes = excel.encode();
    if (encodedBytes == null) {
      throw Exception('Erreur lors de l\'encodage du modèle Excel');
    }
    return Uint8List.fromList(encodedBytes);
  }

  /// Utilitaire pour récupérer la valeur d'une cellule
  String _getCellValue(Sheet sheet, int row, int col) {
    var cell = sheet.cell(
      CellIndex.indexByColumnRow(columnIndex: col, rowIndex: row),
    );
    return cell.value?.toString() ?? '';
  }
}
