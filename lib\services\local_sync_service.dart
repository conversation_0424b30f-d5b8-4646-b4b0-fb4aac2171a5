import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:network_info_plus/network_info_plus.dart';
import 'backup_service.dart';

/// Classe pour représenter le résultat d'une opération de synchronisation
class SyncResult {
  final bool success;
  final String? error;
  final String? details;
  final Map<String, dynamic>? data;

  SyncResult({required this.success, this.error, this.details, this.data});

  @override
  String toString() {
    if (success) {
      return 'SyncResult(success: true${details != null ? ', details: $details' : ''})';
    } else {
      return 'SyncResult(success: false, error: $error${details != null ? ', details: $details' : ''})';
    }
  }
}

class LocalSyncService {
  static final LocalSyncService _instance = LocalSyncService._internal();
  factory LocalSyncService() => _instance;
  LocalSyncService._internal();

  static const int _serverPort = 8080;
  static const String _syncEndpoint = '/sync';
  static const String _discoveryEndpoint = '/discover';

  HttpServer? _server;
  bool _isServerRunning = false;
  String? _localIP;
  final List<String> _discoveredDevices = [];

  /// Démarre le serveur de synchronisation sur cet appareil
  Future<bool> startSyncServer() async {
    try {
      _localIP = await _getLocalIP();
      if (_localIP == null) {
        debugPrint('Impossible d\'obtenir l\'adresse IP locale');
        return false;
      }

      _server = await HttpServer.bind(InternetAddress.anyIPv4, _serverPort);
      _isServerRunning = true;

      debugPrint(
        'Serveur de synchronisation démarré sur $_localIP:$_serverPort',
      );

      _server!.listen((HttpRequest request) async {
        await _handleRequest(request);
      });

      return true;
    } catch (e) {
      debugPrint('Erreur lors du démarrage du serveur: $e');
      return false;
    }
  }

  /// Arrête le serveur de synchronisation
  Future<void> stopSyncServer() async {
    if (_server != null) {
      await _server!.close();
      _server = null;
      _isServerRunning = false;
      debugPrint('Serveur de synchronisation arrêté');
    }
  }

  /// Gère les requêtes HTTP entrantes
  Future<void> _handleRequest(HttpRequest request) async {
    // Ajouter les en-têtes CORS pour permettre les requêtes cross-origin
    request.response.headers.add('Access-Control-Allow-Origin', '*');
    request.response.headers.add(
      'Access-Control-Allow-Methods',
      'GET, POST, OPTIONS',
    );
    request.response.headers.add(
      'Access-Control-Allow-Headers',
      'Content-Type',
    );

    if (request.method == 'OPTIONS') {
      request.response.statusCode = 200;
      await request.response.close();
      return;
    }

    try {
      switch (request.uri.path) {
        case _discoveryEndpoint:
          await _handleDiscovery(request);
          break;
        case _syncEndpoint:
          if (request.method == 'GET') {
            await _handleSyncRequest(request);
          } else if (request.method == 'POST') {
            await _handleSyncReceive(request);
          }
          break;
        default:
          request.response.statusCode = 404;
          request.response.write('Endpoint non trouvé');
      }
    } catch (e) {
      debugPrint('Erreur lors du traitement de la requête: $e');
      request.response.statusCode = 500;
      request.response.write('Erreur serveur: $e');
    }

    await request.response.close();
  }

  /// Gère la découverte d'appareils
  Future<void> _handleDiscovery(HttpRequest request) async {
    final deviceInfo = {
      'device_name': await _getDeviceName(),
      'ip': _localIP,
      'port': _serverPort,
      'timestamp': DateTime.now().toIso8601String(),
      'app_version': '1.0.0',
    };

    request.response.headers.contentType = ContentType.json;
    request.response.write(jsonEncode(deviceInfo));
  }

  /// Gère les demandes de synchronisation (envoi des données)
  Future<void> _handleSyncRequest(HttpRequest request) async {
    try {
      // Créer une sauvegarde complète
      final backupData = await BackupService.createBackup();

      request.response.headers.contentType = ContentType.json;
      request.response.write(
        jsonEncode({
          'status': 'success',
          'data': backupData,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
    } catch (e) {
      request.response.statusCode = 500;
      request.response.write(
        jsonEncode({'status': 'error', 'message': e.toString()}),
      );
    }
  }

  /// Gère la réception de données de synchronisation
  Future<void> _handleSyncReceive(HttpRequest request) async {
    try {
      final body = await utf8.decoder.bind(request).join();
      final data = jsonDecode(body);

      // Restaurer les données reçues
      await BackupService.restoreFromBackup(data);

      request.response.headers.contentType = ContentType.json;
      request.response.write(
        jsonEncode({
          'status': 'success',
          'message': 'Synchronisation réussie',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
    } catch (e) {
      request.response.statusCode = 500;
      request.response.write(
        jsonEncode({'status': 'error', 'message': e.toString()}),
      );
    }
  }

  /// Découvre les appareils sur le réseau local
  Future<List<Map<String, dynamic>>> discoverDevices() async {
    _discoveredDevices.clear();
    final devices = <Map<String, dynamic>>[];

    try {
      final localIP = await _getLocalIP();
      if (localIP == null) return devices;

      // Extraire le sous-réseau (ex: 192.168.1.x)
      final subnet = localIP.substring(0, localIP.lastIndexOf('.'));

      // Scanner les adresses IP du sous-réseau (1-254)
      final futures = <Future>[];

      for (int i = 1; i <= 254; i++) {
        final ip = '$subnet.$i';
        if (ip == localIP) continue; // Ignorer sa propre IP

        futures.add(_checkDevice(ip));
      }

      await Future.wait(futures);

      // Récupérer les informations des appareils découverts
      for (final ip in _discoveredDevices) {
        try {
          final deviceInfo = await _getDeviceInfo(ip);
          if (deviceInfo != null) {
            devices.add(deviceInfo);
          }
        } catch (e) {
          debugPrint('Erreur lors de la récupération des infos de $ip: $e');
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la découverte: $e');
    }

    return devices;
  }

  /// Vérifie si un appareil répond sur une IP donnée
  Future<void> _checkDevice(String ip) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$ip:$_serverPort$_discoveryEndpoint'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 5)); // Timeout augmenté

      if (response.statusCode == 200) {
        _discoveredDevices.add(ip);
        debugPrint('Appareil trouvé sur $ip');
      }
    } catch (e) {
      // Ignorer les erreurs (appareil non disponible)
      if (kDebugMode) {
        debugPrint('Pas d\'appareil sur $ip: $e');
      }
    }
  }

  /// Récupère les informations d'un appareil
  Future<Map<String, dynamic>?> _getDeviceInfo(String ip) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$ip:$_serverPort$_discoveryEndpoint'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des infos de $ip: $e');
    }
    return null;
  }

  /// Synchronise avec un appareil distant avec retry et diagnostics améliorés
  Future<SyncResult> syncWithDevice(String targetIP) async {
    debugPrint('Début de la synchronisation avec $targetIP');

    // Test de connectivité d'abord
    final connectivityTest = await _testConnectivity(targetIP);
    if (!connectivityTest.success) {
      return SyncResult(
        success: false,
        error: connectivityTest.error,
        details: 'Test de connectivité échoué',
      );
    }

    try {
      // 1. Récupérer les données de l'appareil distant avec retry
      debugPrint('Récupération des données de $targetIP...');
      final getResult = await _getRemoteDataWithRetry(targetIP);
      if (!getResult.success) {
        return getResult;
      }

      final remoteData = getResult.data;

      // 2. Restaurer les données localement
      debugPrint('Restauration des données locales...');
      try {
        await BackupService.restoreFromBackup(remoteData!);
      } catch (e) {
        return SyncResult(
          success: false,
          error: 'Erreur de restauration: $e',
          details: 'Échec lors de la restauration des données reçues',
        );
      }

      // 3. Envoyer nos données à l'appareil distant
      debugPrint('Envoi de nos données à $targetIP...');
      final sendResult = await _sendLocalDataWithRetry(targetIP);
      if (!sendResult.success) {
        return sendResult;
      }

      debugPrint('Synchronisation réussie avec $targetIP');
      return SyncResult(
        success: true,
        details: 'Synchronisation bidirectionnelle réussie',
      );
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation: $e');
      return SyncResult(
        success: false,
        error: e.toString(),
        details: 'Erreur inattendue pendant la synchronisation',
      );
    }
  }

  /// Test de connectivité avec un appareil
  Future<SyncResult> _testConnectivity(String targetIP) async {
    try {
      debugPrint('Test de connectivité avec $targetIP...');
      final response = await http
          .get(
            Uri.parse('http://$targetIP:$_serverPort$_discoveryEndpoint'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        return SyncResult(success: true);
      } else {
        return SyncResult(
          success: false,
          error: 'Serveur non disponible (HTTP ${response.statusCode})',
          details: 'L\'appareil répond mais le service n\'est pas disponible',
        );
      }
    } catch (e) {
      String errorMessage = 'Connexion impossible';
      String details = 'Vérifiez que l\'appareil est sur le même réseau WiFi';

      if (e.toString().contains('TimeoutException')) {
        errorMessage = 'Timeout de connexion';
        details = 'L\'appareil ne répond pas dans les temps';
      } else if (e.toString().contains('SocketException')) {
        errorMessage = 'Erreur réseau';
        details = 'Impossible de joindre l\'appareil sur le réseau';
      }

      return SyncResult(success: false, error: errorMessage, details: details);
    }
  }

  /// Récupère les données distantes avec retry
  Future<SyncResult> _getRemoteDataWithRetry(String targetIP) async {
    const maxRetries = 3;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint(
          'Tentative $attempt/$maxRetries de récupération des données...',
        );

        final response = await http
            .get(
              Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
              headers: {'Connection': 'close'},
            )
            .timeout(const Duration(seconds: 45));

        if (response.statusCode == 200) {
          final remoteData = jsonDecode(response.body);
          return SyncResult(success: true, data: remoteData);
        } else {
          debugPrint('Erreur HTTP ${response.statusCode}: ${response.body}');
          if (attempt == maxRetries) {
            return SyncResult(
              success: false,
              error: 'Erreur serveur (HTTP ${response.statusCode})',
              details: 'Le serveur distant a retourné une erreur',
            );
          }
        }
      } catch (e) {
        debugPrint('Tentative $attempt échouée: $e');
        if (attempt == maxRetries) {
          return SyncResult(
            success: false,
            error: 'Échec après $maxRetries tentatives',
            details: e.toString(),
          );
        }
        // Attendre avant la prochaine tentative
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }

    return SyncResult(
      success: false,
      error: 'Échec après toutes les tentatives',
      details: 'Impossible de récupérer les données distantes',
    );
  }

  /// Envoie les données locales avec retry
  Future<SyncResult> _sendLocalDataWithRetry(String targetIP) async {
    const maxRetries = 3;

    try {
      final localData = await BackupService.createBackup();

      for (int attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          debugPrint('Tentative $attempt/$maxRetries d\'envoi des données...');

          final sendResponse = await http
              .post(
                Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
                headers: {
                  'Content-Type': 'application/json',
                  'Connection': 'close',
                },
                body: jsonEncode({
                  'data': localData,
                  'timestamp': DateTime.now().toIso8601String(),
                }),
              )
              .timeout(const Duration(seconds: 45));

          if (sendResponse.statusCode == 200) {
            return SyncResult(success: true);
          } else {
            debugPrint(
              'Erreur HTTP ${sendResponse.statusCode}: ${sendResponse.body}',
            );
            if (attempt == maxRetries) {
              return SyncResult(
                success: false,
                error:
                    'Erreur lors de l\'envoi (HTTP ${sendResponse.statusCode})',
                details: 'Le serveur distant n\'a pas pu traiter nos données',
              );
            }
          }
        } catch (e) {
          debugPrint('Tentative $attempt d\'envoi échouée: $e');
          if (attempt == maxRetries) {
            return SyncResult(
              success: false,
              error: 'Échec d\'envoi après $maxRetries tentatives',
              details: e.toString(),
            );
          }
          // Attendre avant la prochaine tentative
          await Future.delayed(Duration(seconds: attempt * 2));
        }
      }
    } catch (e) {
      return SyncResult(
        success: false,
        error: 'Erreur lors de la création de la sauvegarde',
        details: e.toString(),
      );
    }

    return SyncResult(
      success: false,
      error: 'Échec après toutes les tentatives d\'envoi',
      details: 'Impossible d\'envoyer les données locales',
    );
  }

  /// Obtient l'adresse IP locale
  Future<String?> _getLocalIP() async {
    try {
      final info = NetworkInfo();
      return await info.getWifiIP();
    } catch (e) {
      debugPrint('Erreur lors de l\'obtention de l\'IP: $e');
      return null;
    }
  }

  /// Obtient le nom de l'appareil
  Future<String> _getDeviceName() async {
    try {
      final info = NetworkInfo();
      final wifiName = await info.getWifiName();
      return 'HCP-DESIGN-${wifiName ?? 'Device'}';
    } catch (e) {
      return 'HCP-DESIGN-Device';
    }
  }

  /// Diagnostic réseau pour identifier les problèmes de connectivité
  Future<Map<String, dynamic>> networkDiagnostic() async {
    final diagnostic = <String, dynamic>{};

    try {
      // Test de l'IP locale
      final localIP = await _getLocalIP();
      diagnostic['local_ip'] = localIP;
      diagnostic['local_ip_status'] = localIP != null ? 'OK' : 'ERREUR';

      // Test du serveur local
      diagnostic['server_running'] = _isServerRunning;
      diagnostic['server_port'] = _serverPort;

      // Test de connectivité WiFi
      final info = NetworkInfo();
      final wifiName = await info.getWifiName();
      diagnostic['wifi_name'] = wifiName;
      diagnostic['wifi_status'] = wifiName != null ? 'Connecté' : 'Déconnecté';

      // Test de connectivité locale (ping vers la passerelle)
      if (localIP != null) {
        final subnet = localIP.substring(0, localIP.lastIndexOf('.'));
        final gateway = '$subnet.1'; // Généralement la passerelle

        try {
          await http
              .get(Uri.parse('http://$gateway'))
              .timeout(const Duration(seconds: 3));
          diagnostic['gateway_reachable'] = true;
        } catch (e) {
          diagnostic['gateway_reachable'] = false;
          diagnostic['gateway_error'] = e.toString();
        }
      }

      // Nombre d'appareils découverts
      diagnostic['discovered_devices_count'] = _discoveredDevices.length;
      diagnostic['discovered_devices'] = List.from(_discoveredDevices);
    } catch (e) {
      diagnostic['diagnostic_error'] = e.toString();
    }

    return diagnostic;
  }

  /// Test de connectivité spécifique avec un appareil
  Future<Map<String, dynamic>> testDeviceConnectivity(String targetIP) async {
    final test = <String, dynamic>{};
    test['target_ip'] = targetIP;
    test['timestamp'] = DateTime.now().toIso8601String();

    try {
      // Test 1: Ping simple
      final pingStart = DateTime.now();
      try {
        final response = await http
            .get(
              Uri.parse('http://$targetIP:$_serverPort$_discoveryEndpoint'),
              headers: {'Connection': 'close'},
            )
            .timeout(const Duration(seconds: 5));

        final pingDuration = DateTime.now().difference(pingStart);
        test['ping_success'] = true;
        test['ping_duration_ms'] = pingDuration.inMilliseconds;
        test['ping_status_code'] = response.statusCode;
      } catch (e) {
        test['ping_success'] = false;
        test['ping_error'] = e.toString();
      }

      // Test 2: Test du service de synchronisation
      try {
        final syncStart = DateTime.now();
        final response = await http
            .get(
              Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
              headers: {'Connection': 'close'},
            )
            .timeout(const Duration(seconds: 10));

        final syncDuration = DateTime.now().difference(syncStart);
        test['sync_service_available'] = response.statusCode == 200;
        test['sync_duration_ms'] = syncDuration.inMilliseconds;
        test['sync_status_code'] = response.statusCode;
      } catch (e) {
        test['sync_service_available'] = false;
        test['sync_error'] = e.toString();
      }
    } catch (e) {
      test['test_error'] = e.toString();
    }

    return test;
  }

  // Getters
  bool get isServerRunning => _isServerRunning;
  String? get localIP => _localIP;
  List<String> get discoveredDevices => List.from(_discoveredDevices);
}
