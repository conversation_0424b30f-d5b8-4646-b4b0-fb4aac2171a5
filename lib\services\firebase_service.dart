import 'dart:async';
import 'dart:convert';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import 'package:general_hcp_crm/models/category.dart' as my_models;
import '../models/invoice.dart';
import 'package:general_hcp_crm/models/task.dart' as my_models;
import '../models/colis.dart';
import '../models/user.dart' as app_models;
import 'user_service.dart';

class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance {
    _instance ??= FirebaseService._internal();
    return _instance!;
  }

  FirebaseService._internal();

  // Firebase instances
  late final FirebaseDatabase _database;
  late final FirebaseAuth _auth;

  // Connection status
  bool _isInitialized = false;
  bool _isOnline = false;
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  // Local cache keys
  static const String _pendingOperationsKey = 'firebase_pending_operations';
  static const String _lastSyncTimeKey = 'firebase_last_sync_time';

  // Pending operations queue
  final List<Map<String, dynamic>> _pendingOperations = [];

  // Initialiser Firebase en arrière-plan
  void initializeInBackground() {
    if (_isInitialized) return;
    
    // Utiliser Future.microtask pour ne pas bloquer l'interface utilisateur
    Future.microtask(() async {
      try {
        await initialize();
        debugPrint('Firebase service initialized in background');
      } catch (e) {
        debugPrint('Error initializing Firebase service in background: $e');
      }
    });
  }

  // Initialize Firebase
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Firebase
      await Firebase.initializeApp();

      // Initialize services
      _database = FirebaseDatabase.instance;
      _auth = FirebaseAuth.instance;

      // Load pending operations
      await _loadPendingOperations();

      // Setup connectivity monitoring
      final connectivityResult = await Connectivity().checkConnectivity();
      _isOnline = connectivityResult != ConnectivityResult.none;

      _connectivitySubscription = Connectivity().onConnectivityChanged.listen((
        result,
      ) {
        final wasOffline = !_isOnline;
        _isOnline = result != ConnectivityResult.none;

        // If we just came back online, process pending operations
        if (wasOffline && _isOnline) {
          _processPendingOperations();
        }
      });

      _isInitialized = true;
      debugPrint('Firebase service initialized successfully');

      // If online, process any pending operations
      if (_isOnline) {
        _processPendingOperations();
      }
    } catch (e) {
      debugPrint('Error initializing Firebase: $e');
      rethrow;
    }
  }

  // Ensure initialization
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // AUTHENTICATION METHODS

  // Sign in anonymously if no user is signed in
  Future<User?> _ensureAuthenticated() async {
    await _ensureInitialized();

    if (_auth.currentUser == null) {
      final userCredential = await _auth.signInAnonymously();
      return userCredential.user;
    }

    return _auth.currentUser;
  }

  // CRUD OPERATIONS WITH OFFLINE SUPPORT

  // Generic method to add data to Firebase
  Future<String> addData(String path, Map<String, dynamic> data) async {
    await _ensureInitialized();
    await _ensureAuthenticated();

    final ref = _database.ref(path);
    final newKey = ref.push().key;

    if (newKey == null) {
      throw Exception('Failed to generate new key');
    }

    data['id'] = newKey;

    // If online, push directly to Firebase
    if (_isOnline) {
      try {
        await ref.child(newKey).set(data);
        return newKey;
      } catch (e) {
        // If failed, queue for later
        _addPendingOperation('add', path, newKey, data);
        return newKey;
      }
    } else {
      // If offline, queue for later
      _addPendingOperation('add', path, newKey, data);
      return newKey;
    }
  }

  // Generic method to update data in Firebase
  Future<void> updateData(
    String path,
    String id,
    Map<String, dynamic> data,
  ) async {
    await _ensureInitialized();
    await _ensureAuthenticated();

    final itemPath = '$path/$id';

    // If online, update directly in Firebase
    if (_isOnline) {
      try {
        await _database.ref(itemPath).update(data);
      } catch (e) {
        // If failed, queue for later
        _addPendingOperation('update', path, id, data);
      }
    } else {
      // If offline, queue for later
      _addPendingOperation('update', path, id, data);
    }
  }

  // Generic method to delete data from Firebase
  Future<void> deleteData(String path, String id) async {
    await _ensureInitialized();
    await _ensureAuthenticated();

    final itemPath = '$path/$id';

    // If online, delete directly from Firebase
    if (_isOnline) {
      try {
        await _database.ref(itemPath).remove();
      } catch (e) {
        // If failed, queue for later
        _addPendingOperation('delete', path, id, null);
      }
    } else {
      // If offline, queue for later
      _addPendingOperation('delete', path, id, null);
    }
  }

  // Generic method to get data from Firebase
  Future<Map<String, dynamic>?> getData(String path, String id) async {
    await _ensureInitialized();
    await _ensureAuthenticated();

    if (!_isOnline) {
      // If offline, return null and let the app use local data
      return null;
    }

    final itemPath = '$path/$id';
    final snapshot = await _database.ref(itemPath).get();

    if (snapshot.exists) {
      return Map<String, dynamic>.from(snapshot.value as Map);
    }

    return null;
  }

  // Generic method to get all data from a path
  Future<List<Map<String, dynamic>>> getAllData(String path) async {
    await _ensureInitialized();
    await _ensureAuthenticated();

    if (!_isOnline) {
      // If offline, return empty list and let the app use local data
      return [];
    }

    final snapshot = await _database.ref(path).get();

    if (snapshot.exists && snapshot.value != null) {
      final data = Map<String, dynamic>.from(snapshot.value as Map);
      return data.entries
          .map((e) => Map<String, dynamic>.from(e.value as Map))
          .toList();
    }

    return [];
  }

  // SPECIFIC DATA TYPE METHODS

  // Products
  Future<String> addProduct(Product product) async {
    final data = product.toJson();
    return addData('products', data);
  }

  Future<void> updateProduct(Product product) async {
    final data = product.toJson();
    await updateData('products', product.id, data);
  }

  Future<void> deleteProduct(String id) async {
    await deleteData('products', id);
  }

  Future<List<Product>> getAllProducts() async {
    final data = await getAllData('products');
    return data.map((json) => Product.fromJson(json)).toList();
  }

  // Categories
  Future<String> addCategory(my_models.Category category) async {
    final data = category.toJson();
    return addData('categories', data);
  }

  Future<void> updateCategory(my_models.Category category) async {
    final data = category.toJson();
    await updateData('categories', category.id, data);
  }

  Future<void> deleteCategory(String id) async {
    await deleteData('categories', id);
  }

  Future<List<my_models.Category>> getAllCategories() async {
    final data = await getAllData('categories');
    return data.map((json) => my_models.Category.fromJson(json)).toList();
  }

  // Invoices
  Future<String> addInvoice(Invoice invoice) async {
    final data = invoice.toJson();
    return addData('invoices', data);
  }

  Future<void> updateInvoice(Invoice invoice) async {
    final data = invoice.toJson();
    await updateData('invoices', invoice.id, data);
  }

  Future<void> deleteInvoice(String id) async {
    await deleteData('invoices', id);
  }

  Future<List<Invoice>> getAllInvoices() async {
    final data = await getAllData('invoices');
    return data.map((json) => Invoice.fromJson(json)).toList();
  }

  // Tasks
  Future<String> addTask(my_models.Task task) async {
    final data = task.toJson();
    return addData('tasks', data);
  }

  Future<void> updateTask(my_models.Task task) async {
    final data = task.toJson();
    await updateData('tasks', task.id, data);
  }

  Future<void> deleteTask(String id) async {
    await deleteData('tasks', id);
  }

  Future<List<my_models.Task>> getAllTasks() async {
    final data = await getAllData('tasks');
    return data.map((json) => my_models.Task.fromJson(json)).toList();
  }

  // Colis
  Future<String> addColis(Colis colis) async {
    // Vérifier les permissions
    final userService = UserService();
    if (!userService.canManageColis()) {
      throw Exception('Permissions insuffisantes pour gérer les colis');
    }

    final data = colis.toJson();
    // Ajouter l'ID de l'utilisateur qui a créé le colis
    data['createdBy'] = userService.currentUser?.id;
    data['createdAt'] = DateTime.now().toIso8601String();
    
    return addData('colis', data);
  }

  Future<void> updateColis(Colis colis) async {
    // Vérifier les permissions
    final userService = UserService();
    if (!userService.canManageColis()) {
      throw Exception('Permissions insuffisantes pour gérer les colis');
    }

    final data = colis.toJson();
    // Ajouter l'ID de l'utilisateur qui a modifié le colis
    data['modifiedBy'] = userService.currentUser?.id;
    data['modifiedAt'] = DateTime.now().toIso8601String();
    
    await updateData('colis', colis.id, data);
  }

  Future<void> deleteColis(String id) async {
    // Vérifier les permissions
    final userService = UserService();
    if (!userService.canManageColis()) {
      throw Exception('Permissions insuffisantes pour gérer les colis');
    }

    await deleteData('colis', id);
  }

  Future<List<Colis>> getAllColis() async {
    final data = await getAllData('colis');
    return data.map((json) => Colis.fromJson(json)).toList();
  }

  // Mettre à jour le statut de livraison d'un colis
  Future<void> updateColisStatus(String colisId, String newStatus) async {
    final userService = UserService();
    if (!userService.canManageColis()) {
      throw Exception('Permissions insuffisantes pour modifier le statut des colis');
    }

    final updateData = {
      'statut': newStatus,
      'statusUpdatedBy': userService.currentUser?.id,
      'statusUpdatedAt': DateTime.now().toIso8601String(),
    };

    // Si le statut est "livré", ajouter la date de livraison
    if (newStatus.toLowerCase() == 'livré' || newStatus.toLowerCase() == 'delivered') {
      updateData['dateLivraison'] = DateTime.now().toIso8601String();
    }

    await this.updateData('colis', colisId, updateData);
  }

  // Obtenir les colis par statut
  Future<List<Colis>> getColisByStatus(String status) async {
    final allColis = await getAllColis();
    return allColis.where((colis) => colis.statut.toLowerCase() == status.toLowerCase()).toList();
  }

  // Obtenir les colis d'un utilisateur spécifique
  Future<List<Colis>> getColisByUser(String userId) async {
    final allColis = await getAllColis();
    return allColis.where((colis) => 
      (colis.toJson()['createdBy'] == userId) || 
      (colis.toJson()['assignedTo'] == userId)
    ).toList();
  }

  // Users (gestion des utilisateurs)
  Future<String> addUser(app_models.User user) async {
    // Seuls les admins peuvent ajouter des utilisateurs
    final userService = UserService();
    if (!userService.canManageUsers()) {
      throw Exception('Permissions insuffisantes pour gérer les utilisateurs');
    }

    final data = user.toJson();
    return addData('users', data);
  }

  Future<void> updateUser(app_models.User user) async {
    final userService = UserService();
    // Un utilisateur peut modifier son propre profil ou un admin peut modifier n'importe quel profil
    if (userService.currentUser?.id != user.id && !userService.canManageUsers()) {
      throw Exception('Permissions insuffisantes pour modifier cet utilisateur');
    }

    final data = user.toJson();
    await updateData('users', user.id, data);
  }

  Future<void> deleteUser(String id) async {
    final userService = UserService();
    if (!userService.canManageUsers()) {
      throw Exception('Permissions insuffisantes pour supprimer des utilisateurs');
    }

    await deleteData('users', id);
  }

  Future<List<app_models.User>> getAllUsers() async {
    final userService = UserService();
    if (!userService.canManageUsers()) {
      throw Exception('Permissions insuffisantes pour voir la liste des utilisateurs');
    }

    final data = await getAllData('users');
    return data.map((json) => app_models.User.fromJson(json)).toList();
  }

  // OFFLINE SUPPORT METHODS

  // Add operation to pending queue
  void _addPendingOperation(
    String operation,
    String path,
    String id,
    Map<String, dynamic>? data,
  ) {
    _pendingOperations.add({
      'operation': operation,
      'path': path,
      'id': id,
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    _savePendingOperations();
  }

  // Save pending operations to SharedPreferences
  Future<void> _savePendingOperations() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonData = jsonEncode(_pendingOperations);
    await prefs.setString(_pendingOperationsKey, jsonData);
  }

  // Load pending operations from SharedPreferences
  Future<void> _loadPendingOperations() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonData = prefs.getString(_pendingOperationsKey);

    if (jsonData != null) {
      final List<dynamic> operations = jsonDecode(jsonData);
      _pendingOperations.clear();
      _pendingOperations.addAll(
        operations.map((op) => Map<String, dynamic>.from(op)).toList(),
      );
    }
  }

  // Process pending operations when online
  Future<void> _processPendingOperations() async {
    if (!_isOnline || _pendingOperations.isEmpty) return;

    // Sort operations by timestamp
    _pendingOperations.sort(
      (a, b) => (a['timestamp'] as int).compareTo(b['timestamp'] as int),
    );

    // Process each operation
    final List<Map<String, dynamic>> completedOperations = [];

    for (final operation in _pendingOperations) {
      try {
        final type = operation['operation'] as String;
        final path = operation['path'] as String;
        final id = operation['id'] as String;
        final data = operation['data'] as Map<String, dynamic>?;

        switch (type) {
          case 'add':
            if (data != null) {
              await _database.ref('$path/$id').set(data);
            }
            break;
          case 'update':
            if (data != null) {
              await _database.ref('$path/$id').update(data);
            }
            break;
          case 'delete':
            await _database.ref('$path/$id').remove();
            break;
        }

        completedOperations.add(operation);
      } catch (e) {
        debugPrint('Error processing operation: $e');
        // Stop processing on first error
        break;
      }
    }

    // Remove completed operations
    for (final op in completedOperations) {
      _pendingOperations.remove(op);
    }

    // Save updated pending operations
    await _savePendingOperations();

    // Update last sync time
    if (completedOperations.isNotEmpty) {
      await _updateLastSyncTime();
    }
  }

  // Update last sync time
  Future<void> _updateLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now().toIso8601String();
    await prefs.setString(_lastSyncTimeKey, now);
  }

  // Get last sync time
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timeString = prefs.getString(_lastSyncTimeKey);

    if (timeString != null) {
      return DateTime.parse(timeString);
    }

    return null;
  }

  // Check if there are pending operations
  bool hasPendingOperations() {
    return _pendingOperations.isNotEmpty;
  }

  // Get number of pending operations
  int getPendingOperationsCount() {
    return _pendingOperations.length;
  }

  // Check if device is online
  bool isOnline() {
    return _isOnline;
  }

  // Force sync now
  Future<bool> forceSyncNow() async {
    if (!_isOnline) return false;

    await _processPendingOperations();
    return true;
  }

  // Dispose
  void dispose() {
    _connectivitySubscription?.cancel();
  }
}
