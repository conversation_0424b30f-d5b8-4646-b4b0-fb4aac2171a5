import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BiometricAuthService {
  static final BiometricAuthService _instance = BiometricAuthService._internal();
  factory BiometricAuthService() => _instance;
  BiometricAuthService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _firstLaunchKey = 'first_launch';

  /// Vérifie si l'appareil supporte l'authentification biométrique
  Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      final bool isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      debugPrint('Erreur lors de la vérification de la disponibilité biométrique: $e');
      return false;
    }
  }

  /// Obtient la liste des types d'authentification biométrique disponibles
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      debugPrint('Erreur lors de la récupération des biométriques disponibles: $e');
      return [];
    }
  }

  /// Vérifie si l'authentification biométrique est activée dans les paramètres
  Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_biometricEnabledKey) ?? false;
  }

  /// Active ou désactive l'authentification biométrique
  Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_biometricEnabledKey, enabled);
  }

  /// Vérifie si c'est le premier lancement de l'application
  Future<bool> isFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_firstLaunchKey) ?? true;
  }

  /// Marque que l'application a été lancée au moins une fois
  Future<void> setFirstLaunchCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_firstLaunchKey, false);
  }

  /// Effectue l'authentification biométrique
  Future<bool> authenticate() async {
    try {
      final bool isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'Placez votre doigt sur le capteur d\'empreinte digitale pour accéder à l\'application',
        options: const AuthenticationOptions(
          biometricOnly: true, // Forcer l'utilisation de la biométrie uniquement
          stickyAuth: true,
          useErrorDialogs: true,
          sensitiveTransaction: false,
        ),
      );
      return isAuthenticated;
    } catch (e) {
      debugPrint('Erreur lors de l\'authentification: $e');
      return false;
    }
  }

  /// Demande à l'utilisateur s'il souhaite activer l'authentification biométrique
  Future<bool?> showBiometricSetupDialog(BuildContext context) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sécurité de l\'application'),
          content: const Text(
            'Voulez-vous activer l\'authentification par empreinte digitale pour sécuriser l\'accès à votre application CRM ?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Plus tard'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Activer'),
            ),
          ],
        );
      },
    );
  }

  /// Affiche une boîte de dialogue d'erreur d'authentification
  Future<void> showAuthenticationErrorDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Authentification échouée'),
          content: const Text(
            'L\'authentification biométrique a échoué. Veuillez réessayer.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Vérifie si l'authentification est requise et effectue l'authentification si nécessaire
  Future<bool> checkAndAuthenticate(BuildContext context) async {
    // Vérifier si l'authentification biométrique est activée
    final bool isEnabled = await isBiometricEnabled();
    if (!isEnabled) {
      return true; // Pas d'authentification requise
    }

    // Vérifier si l'appareil supporte l'authentification biométrique
    final bool isAvailable = await isBiometricAvailable();
    if (!isAvailable) {
      // Désactiver l'authentification biométrique si elle n'est plus disponible
      await setBiometricEnabled(false);
      return true;
    }

    // Effectuer l'authentification
    final bool isAuthenticated = await authenticate();
    if (!isAuthenticated && context.mounted) {
      await showAuthenticationErrorDialog(context);
    }
    
    return isAuthenticated;
  }

  /// Configure l'authentification biométrique lors du premier lancement
  Future<void> setupBiometricOnFirstLaunch(BuildContext context) async {
    final bool isFirstLaunch = await this.isFirstLaunch();
    if (!isFirstLaunch) return;

    final bool isAvailable = await isBiometricAvailable();
    if (!isAvailable) {
      await setFirstLaunchCompleted();
      return;
    }

    if (context.mounted) {
      final bool? enableBiometric = await showBiometricSetupDialog(context);
      if (enableBiometric == true) {
        await setBiometricEnabled(true);
      }
    }
    
    await setFirstLaunchCompleted();
  }
}