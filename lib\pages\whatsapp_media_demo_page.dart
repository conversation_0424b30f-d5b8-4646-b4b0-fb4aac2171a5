import 'dart:io';
import 'package:flutter/material.dart';
import '../services/media_service.dart' as media_service;

class WhatsAppMediaDemoPage extends StatefulWidget {
  const WhatsAppMediaDemoPage({super.key});

  @override
  State<WhatsAppMediaDemoPage> createState() => _WhatsAppMediaDemoPageState();
}

class _WhatsAppMediaDemoPageState extends State<WhatsAppMediaDemoPage> {
  final media_service.MediaService _mediaService = media_service.MediaService();

  bool _isLoading = false;
  String? _lastSavedPath;
  String _status = 'Prêt à tester la sauvegarde de médias WhatsApp';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Démo Sauvegarde Médias WhatsApp'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildTestCard(),
            const SizedBox(height: 16),
            _buildStatusCard(),
            if (_lastSavedPath != null) ...[
              const SizedBox(height: 16),
              _buildResultCard(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Sauvegarde Locale des Médias',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Cette fonctionnalité permet de sauvegarder automatiquement tous les médias reçus via WhatsApp (images, vidéos, documents, audio) dans le stockage local de l\'application.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              '✅ Support des images (JPEG, PNG, GIF, WebP)\n'
              '✅ Support des vidéos (MP4, AVI, MOV)\n'
              '✅ Support de l\'audio (MP3, WAV, AAC, AMR)\n'
              '✅ Support des documents (PDF, DOC, XLS, TXT)',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.play_arrow, color: Colors.green[700]),
                const SizedBox(width: 8),
                const Text(
                  'Test de Sauvegarde',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Testez la sauvegarde locale en sélectionnant un fichier média.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        _isLoading ? null : () => _testMediaSave('image'),
                    icon: const Icon(Icons.image),
                    label: const Text('Test Image'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[700],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        _isLoading ? null : () => _testMediaSave('video'),
                    icon: const Icon(Icons.videocam),
                    label: const Text('Test Vidéo'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[700],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        _isLoading ? null : () => _testMediaSave('audio'),
                    icon: const Icon(Icons.audiotrack),
                    label: const Text('Test Audio'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange[700],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        _isLoading ? null : () => _testMediaSave('document'),
                    icon: const Icon(Icons.description),
                    label: const Text('Test Document'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[700],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isLoading ? Icons.hourglass_empty : Icons.check_circle,
                  color: _isLoading ? Colors.orange[700] : Colors.green[700],
                ),
                const SizedBox(width: 8),
                const Text(
                  'Statut',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_isLoading)
              const LinearProgressIndicator()
            else
              Text(_status, style: const TextStyle(fontSize: 14)),
          ],
        ),
      ),
    );
  }

  Widget _buildResultCard() {
    return Card(
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green[700]),
                const SizedBox(width: 8),
                const Text(
                  'Fichier Sauvegardé',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Chemin: $_lastSavedPath',
              style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
            ),
            const SizedBox(height: 8),
            if (_lastSavedPath != null && File(_lastSavedPath!).existsSync())
              Text(
                'Taille: ${_formatFileSize(File(_lastSavedPath!).lengthSync())}',
                style: const TextStyle(fontSize: 12),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _testMediaSave(String mediaType) async {
    setState(() {
      _isLoading = true;
      _status = 'Sélection du fichier $mediaType...';
    });

    try {
      media_service.MediaFile? mediaFile;

      switch (mediaType) {
        case 'image':
          mediaFile = await _mediaService.pickImage();
          break;
        case 'video':
          mediaFile = await _mediaService.pickVideo();
          break;
        case 'audio':
          mediaFile = await _mediaService.pickAudio();
          break;
        case 'document':
          mediaFile = await _mediaService.pickDocument();
          break;
      }

      if (mediaFile != null) {
        setState(() {
          _status = 'Sauvegarde du fichier en cours...';
        });

        final savedPath = await _mediaService.saveMediaFile(mediaFile);

        if (savedPath != null) {
          setState(() {
            _lastSavedPath = savedPath;
            _status = 'Fichier sauvegardé avec succès !';
          });
        } else {
          setState(() {
            _status = 'Erreur lors de la sauvegarde du fichier.';
          });
        }
      } else {
        setState(() {
          _status = 'Aucun fichier sélectionné.';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Erreur: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
