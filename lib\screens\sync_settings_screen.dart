import 'package:flutter/material.dart';
import '../services/sync_service.dart';
import '../widgets/sync_status_widget.dart';

class SyncSettingsScreen extends StatefulWidget {
  const SyncSettingsScreen({super.key});

  @override
  State<SyncSettingsScreen> createState() => _SyncSettingsScreenState();
}

class _SyncSettingsScreenState extends State<SyncSettingsScreen> {
  final SyncService _syncService = SyncService.instance;
  bool _isLoading = false;
  bool _resetConfirmationVisible = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Paramètres de synchronisation')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SyncStatusWidget(showControls: true, compact: false),
                    const SizedBox(height: 24),
                    _buildSectionTitle('Informations'),
                    _buildInfoCard(),
                    const SizedBox(height: 24),
                    _buildSectionTitle('Actions avancées'),
                    _buildAdvancedActions(),
                    if (_resetConfirmationVisible) ...[
                      const SizedBox(height: 16),
                      _buildResetConfirmation(),
                    ],
                  ],
                ),
              ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow(
              icon: Icons.info_outline,
              title: 'À propos de la synchronisation',
              content:
                  'La synchronisation permet de sauvegarder vos données dans le cloud et de les synchroniser entre plusieurs appareils.',
            ),
            const Divider(),
            _buildInfoRow(
              icon: Icons.cloud_upload,
              title: 'Données synchronisées',
              content:
                  'Produits, catégories, factures et tâches sont automatiquement synchronisés avec Firebase.',
            ),
            const Divider(),
            _buildInfoRow(
              icon: Icons.wifi_off,
              title: 'Mode hors ligne',
              content:
                  'L\'application fonctionne hors ligne. Les modifications sont synchronisées automatiquement lorsque la connexion est rétablie.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 24),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 4),
                Text(content),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedActions() {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.sync),
            title: const Text('Forcer la synchronisation'),
            subtitle: const Text('Synchroniser toutes les données maintenant'),
            onTap: _forceSyncNow,
          ),
          const Divider(height: 1),
          FutureBuilder<bool>(
            future: _syncService.isInitialMigrationDone(),
            builder: (context, snapshot) {
              final migrationDone = snapshot.data ?? false;
              return ListTile(
                leading: Icon(
                  migrationDone ? Icons.check_circle : Icons.error_outline,
                  color: migrationDone ? Colors.green : Colors.orange,
                ),
                title: const Text('Migration initiale'),
                subtitle: Text(
                  migrationDone
                      ? 'Migration initiale effectuée'
                      : 'Migration initiale non effectuée',
                ),
                trailing:
                    migrationDone
                        ? null
                        : ElevatedButton(
                          onPressed: _performInitialMigration,
                          child: const Text('Migrer'),
                        ),
              );
            },
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.delete_forever, color: Colors.red),
            title: const Text('Réinitialiser la synchronisation'),
            subtitle: const Text(
              'Réinitialiser les paramètres de synchronisation (dangereux)',
            ),
            onTap: () {
              setState(() {
                _resetConfirmationVisible = true;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildResetConfirmation() {
    return Card(
      color: Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.warning, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'Confirmation de réinitialisation',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Cette action va réinitialiser les paramètres de synchronisation et marquer la migration initiale comme non effectuée. Les données existantes ne seront pas supprimées, mais elles pourraient être dupliquées lors de la prochaine synchronisation.',
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _resetConfirmationVisible = false;
                    });
                  },
                  child: const Text('Annuler'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _resetSyncSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Réinitialiser'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _forceSyncNow() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _syncService.syncNow();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Synchronisation terminée'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _performInitialMigration() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Reset migration flag first
      await _syncService.resetInitialMigration();
      // Then force sync which will trigger migration
      await _syncService.syncNow();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Migration initiale terminée'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _resetConfirmationVisible = false;
        });
      }
    }
  }

  Future<void> _resetSyncSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _syncService.resetInitialMigration();
      await _syncService.setAutoSyncEnabled(true);
      await _syncService.setSyncInterval(15);

      if (mounted) {
        setState(() {
          _resetConfirmationVisible = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Paramètres de synchronisation réinitialisés'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
