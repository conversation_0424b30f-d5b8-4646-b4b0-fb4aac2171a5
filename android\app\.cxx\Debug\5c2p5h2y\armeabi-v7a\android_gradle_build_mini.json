{"buildFiles": ["C:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\G-n-ral-HCP-CRM\\android\\app\\.cxx\\Debug\\5c2p5h2y\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\G-n-ral-HCP-CRM\\android\\app\\.cxx\\Debug\\5c2p5h2y\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}