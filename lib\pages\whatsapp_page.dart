import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/whatsapp_chat.dart';
import '../services/whatsapp_service.dart';
import 'whatsapp_chat_page.dart';
import 'ai_configuration_page.dart';
import 'knowledge_base_page.dart';
import 'whatsapp_webhook_config_page.dart';
import 'ai_testing_page.dart';
import 'whatsapp_permissions_page.dart';
import '../screens/sync_settings_screen.dart';
import '../screens/user_management_screen.dart';

class WhatsAppPage extends StatefulWidget {
  const WhatsAppPage({super.key});

  @override
  State<WhatsAppPage> createState() => _WhatsAppPageState();
}

class _WhatsAppPageState extends State<WhatsAppPage>
    with TickerProviderStateMixin {
  final WhatsAppService _whatsappService = WhatsAppService();
  final TextEditingController _searchController = TextEditingController();

  List<WhatsAppChat> _chats = [];
  List<WhatsAppChat> _filteredChats = [];
  bool _isLoading = true;
  String _searchQuery = '';

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadChats();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadChats() async {
    setState(() => _isLoading = true);

    try {
      await _whatsappService.initialize();
      final chats = _whatsappService.getChats();

      setState(() {
        _chats = chats;
        _filteredChats = chats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterChats(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredChats = _chats;
      } else {
        _filteredChats = _whatsappService.searchChats(query);
      }
    });
  }

  void _openChat(WhatsAppChat chat) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(builder: (context) => WhatsAppChatPage(chat: chat)),
        )
        .then((_) {
          // Recharger les chats après retour de la conversation
          _loadChats();
        });
  }

  Future<void> _createNewChat() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _NewChatDialog(),
    );

    if (result != null) {
      try {
        await _whatsappService.createChat(
          customerName: result['name']!,
          customerPhone: result['phone']!,
        );
        await _loadChats();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la création: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WhatsApp Business'),
        backgroundColor: const Color(0xFF25D366), // Couleur WhatsApp
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.security),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const WhatsAppPermissionsPage(),
                ),
              );
            },
            tooltip: 'Permissions médias',
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Focus sur la barre de recherche
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showOptionsMenu();
            },
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildSearchBar(),
              _buildStatsBar(),
              Expanded(
                child:
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : _buildChatsList(),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewChat,
        backgroundColor: const Color(0xFF25D366),
        child: const Icon(Icons.chat, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: const Color(0xFF25D366),
      child: TextField(
        controller: _searchController,
        onChanged: _filterChats,
        decoration: InputDecoration(
          hintText: 'Rechercher une conversation...',
          hintStyle: const TextStyle(color: Colors.grey),
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          suffixIcon:
              _searchQuery.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear, color: Colors.grey),
                    onPressed: () {
                      _searchController.clear();
                      _filterChats('');
                    },
                  )
                  : null,
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildStatsBar() {
    final stats = _whatsappService.getChatStatistics();
    final totalUnread = stats['totalUnreadCount'] as int;
    final activeChats = stats['activeChats'] as int;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey[100],
      child: Row(
        children: [
          _buildStatChip(
            icon: Icons.chat,
            label: '$activeChats conversations',
            color: Colors.blue,
          ),
          const SizedBox(width: 8),
          if (totalUnread > 0)
            _buildStatChip(
              icon: Icons.mark_chat_unread,
              label: '$totalUnread non lus',
              color: Colors.red,
            ),
        ],
      ),
    );
  }

  Widget _buildStatChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatsList() {
    if (_filteredChats.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'Aucune conversation trouvée'
                  : 'Aucune conversation',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty
                  ? 'Essayez un autre terme de recherche'
                  : 'Commencez une nouvelle conversation',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredChats.length,
      itemBuilder: (context, index) {
        final chat = _filteredChats[index];
        return _buildChatItem(chat);
      },
    );
  }

  Widget _buildChatItem(WhatsAppChat chat) {
    final timeFormat = DateFormat('HH:mm');
    final dateFormat = DateFormat('dd/MM');
    final now = DateTime.now();
    final isToday =
        chat.lastMessageTime.day == now.day &&
        chat.lastMessageTime.month == now.month &&
        chat.lastMessageTime.year == now.year;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: const Color(0xFF25D366),
        backgroundImage:
            chat.customerAvatar != null
                ? NetworkImage(chat.customerAvatar!)
                : null,
        child:
            chat.customerAvatar == null
                ? Text(
                  chat.customerName.isNotEmpty
                      ? chat.customerName[0].toUpperCase()
                      : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              chat.customerName,
              style: TextStyle(
                fontWeight:
                    chat.unreadCount > 0 ? FontWeight.bold : FontWeight.w500,
                fontSize: 16,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Text(
            isToday
                ? timeFormat.format(chat.lastMessageTime)
                : dateFormat.format(chat.lastMessageTime),
            style: TextStyle(
              fontSize: 12,
              color:
                  chat.unreadCount > 0
                      ? const Color(0xFF25D366)
                      : Colors.grey[600],
              fontWeight:
                  chat.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
      subtitle: Row(
        children: [
          if (chat.status == ChatStatus.pending)
            Container(
              margin: const EdgeInsets.only(right: 4),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'En attente',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          if (chat.isAiEnabled)
            Container(
              margin: const EdgeInsets.only(right: 4),
              child: Icon(Icons.smart_toy, size: 14, color: Colors.blue[600]),
            ),
          Expanded(
            child: Text(
              chat.lastMessage,
              style: TextStyle(
                color: chat.unreadCount > 0 ? Colors.black87 : Colors.grey[600],
                fontWeight:
                    chat.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          if (chat.unreadCount > 0)
            Container(
              margin: const EdgeInsets.only(left: 8),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: const BoxDecoration(
                color: Color(0xFF25D366),
                shape: BoxShape.circle,
              ),
              child: Text(
                chat.unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      onTap: () => _openChat(chat),
      onLongPress: () => _showChatOptions(chat),
    );
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Permet de faire défiler si nécessaire
      builder:
          (context) => SingleChildScrollView(
            // Assure que tout le contenu est accessible
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Option de gestion des utilisateurs placée en premier pour s'assurer qu'elle est visible
                ListTile(
                  leading: const Icon(Icons.people, color: Colors.blue),
                  title: const Text('Gestion des utilisateurs'),
                  subtitle: const Text('Gérer les comptes utilisateurs'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const UserManagementScreen(),
                      ),
                    );
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.smart_toy),
                  title: const Text('Configuration IA'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AIConfigurationPage(),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.library_books),
                  title: const Text('Base de Connaissances'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const KnowledgeBasePage(),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.webhook),
                  title: const Text('Configuration WhatsApp'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const WhatsAppWebhookConfigPage(),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.science),
                  title: const Text('Test & Monitoring IA'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AITestingPage(),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.analytics),
                  title: const Text('Statistiques'),
                  onTap: () {
                    Navigator.pop(context);
                    _showStatistics();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.archive),
                  title: const Text('Conversations archivées'),
                  onTap: () {
                    Navigator.pop(context);
                    // Afficher les conversations archivées
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.security),
                  title: const Text('Permissions médias'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const WhatsAppPermissionsPage(),
                      ),
                    );
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.cloud_sync),
                  title: const Text('Synchronisation Firebase'),
                  subtitle: const Text('Gérer la synchronisation des données'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SyncSettingsScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _showChatOptions(WhatsAppChat chat) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.archive),
                title: const Text('Archiver'),
                onTap: () async {
                  Navigator.pop(context);
                  await _whatsappService.archiveChat(chat.id);
                  await _loadChats();
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Supprimer'),
                onTap: () async {
                  Navigator.pop(context);
                  final confirm = await showDialog<bool>(
                    context: context,
                    builder:
                        (context) => AlertDialog(
                          title: const Text('Supprimer la conversation'),
                          content: const Text('Cette action est irréversible.'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context, false),
                              child: const Text('Annuler'),
                            ),
                            TextButton(
                              onPressed: () => Navigator.pop(context, true),
                              child: const Text('Supprimer'),
                            ),
                          ],
                        ),
                  );

                  if (confirm == true) {
                    await _whatsappService.deleteChat(chat.id);
                    await _loadChats();
                  }
                },
              ),
            ],
          ),
    );
  }

  void _showStatistics() {
    final stats = _whatsappService.getChatStatistics();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Statistiques WhatsApp'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Conversations totales: ${stats['totalChats']}'),
                Text('Conversations actives: ${stats['activeChats']}'),
                Text('Conversations archivées: ${stats['archivedChats']}'),
                Text('Messages totaux: ${stats['totalMessages']}'),
                Text('Messages IA: ${stats['aiMessages']}'),
                Text('Messages clients: ${stats['customerMessages']}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }
}

class _NewChatDialog extends StatefulWidget {
  @override
  State<_NewChatDialog> createState() => _NewChatDialogState();
}

class _NewChatDialogState extends State<_NewChatDialog> {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Nouvelle conversation'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Nom du client',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'Numéro de téléphone',
              border: OutlineInputBorder(),
              prefixText: '+',
            ),
            keyboardType: TextInputType.phone,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_nameController.text.isNotEmpty &&
                _phoneController.text.isNotEmpty) {
              Navigator.pop(context, {
                'name': _nameController.text,
                'phone': _phoneController.text,
              });
            }
          },
          child: const Text('Créer'),
        ),
      ],
    );
  }
}
