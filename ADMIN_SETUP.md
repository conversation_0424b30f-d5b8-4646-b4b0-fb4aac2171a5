# Configuration Administrateur

## Identifiants Administrateur par Défaut

L'application est configurée avec un compte administrateur par défaut qui sera créé automatiquement au premier lancement.

### Informations de Connexion

- **Email**: `<EMAIL>`
- **Mot de passe**: `Mo<PERSON>wallyd-007`
- **Rôle**: Administrateur
- **Nom**: Administrateur Principal

## Fonctionnalités Administrateur

Avec ce compte administrateur, vous pouvez :

### Gestion des Utilisateurs
- ✅ Créer de nouveaux comptes utilisateur
- ✅ Modifier les rôles et permissions
- ✅ Activer/désactiver des comptes
- ✅ Supprimer des utilisateurs
- ✅ Voir la liste complète des utilisateurs

### Gestion des Données
- ✅ Synchroniser les colis et utilisateurs
- ✅ Accéder à toutes les fonctionnalités de l'application
- ✅ Gérer les produits, factures et tâches
- ✅ Voir les rapports et statistiques

## Accès à la Gestion des Utilisateurs

1. Connectez-vous avec les identifiants administrateur
2. Allez dans la page WhatsApp
3. Cliquez sur le menu trois points (⋮) en haut à droite
4. Sélectionnez "Gestion des utilisateurs"

## Sécurité

⚠️ **Important**: Changez le mot de passe par défaut après la première connexion pour des raisons de sécurité.

### Pour changer le mot de passe :
1. Connectez-vous avec le compte administrateur
2. Allez dans les paramètres de profil
3. Modifiez le mot de passe

## Création d'Autres Administrateurs

1. Connectez-vous en tant qu'administrateur
2. Allez dans "Gestion des utilisateurs"
3. Cliquez sur "Ajouter un utilisateur"
4. Remplissez les informations
5. Sélectionnez le rôle "admin"
6. Cliquez sur "Enregistrer"

## Rôles Disponibles

- **admin**: Accès complet à toutes les fonctionnalités
- **manager**: Gestion des produits, factures et colis
- **employee**: Accès de base aux fonctionnalités principales

## Dépannage

Si vous ne pouvez pas vous connecter :

1. Vérifiez que Firebase est correctement configuré
2. Assurez-vous que l'application a accès à Internet
3. Redémarrez l'application pour relancer l'initialisation
4. Vérifiez les logs de l'application pour les erreurs

## Support

Pour toute question ou problème, consultez les logs de l'application ou contactez le support technique.