import 'package:firebase_core/firebase_core.dart';
import '../services/user_service.dart';
import 'dart:developer' as developer;

/// Script d'initialisation pour créer l'utilisateur administrateur
/// Email: <EMAIL>
/// Mot de passe: Moiwallyd-007
class AdminInitializer {
  static Future<void> createAdminUser() async {
    try {
      // Initialiser Firebase si ce n'est pas déjà fait
      await Firebase.initializeApp();
      
      final userService = UserService();
      await userService.initialize();
      
      // Informations de l'administrateur
      const adminEmail = '<EMAIL>';
      const adminPassword = 'Moiwallyd-007';
      const adminName = 'Administrateur Principal';
      const adminRole = 'admin';
      
      developer.log('Création de l\'utilisateur administrateur...');
      
      // Créer l'utilisateur administrateur
      final adminUser = await userService.createUserWithEmailAndPassword(
        email: adminEmail,
        password: adminPassword,
        name: admin<PERSON><PERSON>,
        role: adminRole,
      );
      
      if (adminUser != null) {
        developer.log('✅ Utilisateur administrateur créé avec succès!');
        developer.log('Email: $adminEmail');
        developer.log('Nom: $adminName');
        developer.log('Rôle: $adminRole');
        developer.log('ID: ${adminUser.id}');
      } else {
        developer.log('❌ Erreur lors de la création de l\'utilisateur administrateur');
      }
    } catch (e) {
      if (e.toString().contains('email-already-in-use')) {
        developer.log('ℹ️  L\'utilisateur administrateur existe déjà avec cet email.');
        developer.log('Vous pouvez vous connecter avec:');
        developer.log('Email: <EMAIL>');
        developer.log('Mot de passe: Moiwallyd-007');
      } else {
        developer.log('❌ Erreur lors de la création de l\'administrateur: $e');
      }
    }
  }
  
  /// Méthode pour vérifier si l'admin existe et le créer si nécessaire
  static Future<void> ensureAdminExists() async {
    try {
      final userService = UserService();
      
      // Essayer de se connecter avec les identifiants admin
      try {
        final adminUser = await userService.signInWithEmailAndPassword(
          '<EMAIL>',
          'Moiwallyd-007',
        );
        
        if (adminUser != null) {
          developer.log('✅ Administrateur trouvé et connecté.');
          await userService.signOut(); // Se déconnecter après vérification
          return;
        }
      } catch (e) {
        // L'utilisateur n'existe pas, on va le créer
        developer.log('Administrateur non trouvé, création en cours...');
      }
      
      // Créer l'administrateur s'il n'existe pas
      await createAdminUser();
    } catch (e) {
      developer.log('Erreur lors de la vérification/création de l\'administrateur: $e');
    }
  }
}

/// Fonction principale pour exécuter le script
void main() async {
  developer.log('=== Initialisation de l\'administrateur ===');
  await AdminInitializer.ensureAdminExists();
  developer.log('=== Fin de l\'initialisation ===');
}