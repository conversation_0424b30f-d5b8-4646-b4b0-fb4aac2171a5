import 'package:flutter/material.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/services/inventory_service.dart';
import 'package:general_hcp_crm/pages/add_edit_product_page.dart'; // Pour naviguer vers la modification

class StockAlertsPage extends StatefulWidget {
  const StockAlertsPage({super.key});

  @override
  State<StockAlertsPage> createState() => _StockAlertsPageState();
}

class _StockAlertsPageState extends State<StockAlertsPage> {
  final InventoryService _inventoryService = InventoryService.instance;
  List<Product> _lowStockProducts = [];
  bool _isLoading = true;
  final int _lowStockThreshold = 5; // Seuil pour stock bas

  @override
  void initState() {
    super.initState();
    _loadLowStockProducts();
  }

  Future<void> _loadLowStockProducts() async {
    setState(() => _isLoading = true);
    try {
      final allProducts = await _inventoryService.getProducts();
      _lowStockProducts =
          allProducts.where((p) => p.quantity <= _lowStockThreshold).toList();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur de chargement des alertes de stock: ${e.toString()}',
            ),
          ),
        );
      }
    }
    setState(() => _isLoading = false);
  }

  void _navigateToEditProduct(Product product) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditProductPage(product: product),
      ),
    );
    if (result == true) {
      _loadLowStockProducts(); // Recharger les données si un produit a été modifié
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HCP-DESIGN - Alertes Stock'),
        backgroundColor: Colors.orange[700], // Orange pour les alertes
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _lowStockProducts.isEmpty
              ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Aucun produit en stock bas (seuil <= $_lowStockThreshold).',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              )
              : ListView.builder(
                itemCount: _lowStockProducts.length,
                itemBuilder: (context, index) {
                  final product = _lowStockProducts[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    elevation: 3,
                    color:
                        product.quantity == 0
                            ? Colors.red
                            : Colors.orange, // Couleurs pleines cohérentes
                    child: ListTile(
                      leading:
                          product.imageUrl != null &&
                                  product.imageUrl!.isNotEmpty
                              ? CircleAvatar(
                                backgroundImage: NetworkImage(
                                  product.imageUrl!,
                                ),
                                onBackgroundImageError:
                                    (e, s) =>
                                        {}, // Gérer l'erreur de chargement d'image
                              )
                              : CircleAvatar(
                                backgroundColor: Colors.white.withValues(
                                  alpha: 0.2,
                                ),
                                child: const Icon(
                                  Icons.inventory_2_outlined,
                                  color: Colors.white,
                                ),
                              ),
                      title: Text(
                        product.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.white, // Texte blanc pour contraster
                        ),
                      ),
                      subtitle: Text(
                        'Quantité restante: ${product.quantity}',
                        style: TextStyle(
                          color: Colors.white.withValues(
                            alpha: 0.9,
                          ), // Texte blanc semi-transparent
                        ),
                      ),
                      trailing: IconButton(
                        icon: const Icon(
                          Icons.edit_note,
                          color: Colors.white, // Icône blanche
                        ),
                        tooltip: 'Modifier le produit',
                        onPressed: () => _navigateToEditProduct(product),
                      ),
                      onTap: () => _navigateToEditProduct(product),
                    ),
                  );
                },
              ),
    );
  }
}
