# General HCP CRM

🚀 **Application de gestion CRM complète développée avec Flutter**

Une solution moderne pour la gestion des clients, produits, factures, colis et utilisateurs avec synchronisation Firebase en temps réel.

## 📋 Fonctionnalités

### 🔐 Gestion des Utilisateurs
- Authentification sécurisée avec Firebase Auth
- Système de rôles (Admin, Manager, Employee)
- Interface d'administration pour la gestion des comptes
- Permissions granulaires par fonctionnalité

### 📦 Gestion des Colis
- Suivi des livraisons en temps réel
- Statuts de livraison avec codes couleur
- Synchronisation bidirectionnelle avec Firebase
- Gestion des zones de livraison

### 💼 Gestion Commerciale
- Gestion des produits et catégories
- Création et édition de factures
- Suivi des stocks et alertes
- Rapports et statistiques

### 🔄 Synchronisation
- Synchronisation automatique avec Firebase
- Support hors ligne avec mise en file d'attente
- Résolution de conflits intelligente
- Sauvegarde et restauration des données

### 💬 Intégration WhatsApp
- Interface de chat intégrée
- Gestion des médias
- Configuration webhook
- Permissions et paramètres

## 🚀 Installation et Configuration

### Prérequis

- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Compte Firebase
- Git

### 1. Cloner le Repository

```bash
git clone https://github.com/Wallymbx/G-n-ral-HCP-CRM.git
cd G-n-ral-HCP-CRM
```

### 2. Installer les Dépendances

```bash
flutter pub get
```

### 3. Configuration Firebase

⚠️ **IMPORTANT** : Vous devez configurer Firebase avant de lancer l'application.

1. Consultez le guide détaillé : [FIREBASE_SETUP.md](FIREBASE_SETUP.md)
2. Créez votre projet Firebase
3. Configurez Authentication et Realtime Database
4. Mettez à jour `web/firebase-config.js` avec votre configuration

### 4. Vérifier la Configuration

```bash
dart run scripts/check_firebase_config.dart
```

### 5. Lancer l'Application

```bash
flutter run
```

## 👤 Compte Administrateur

L'application crée automatiquement un compte administrateur au premier lancement :

- **Email** : `<EMAIL>`
- **Mot de passe** : `Moiwallyd-007`
- **Rôle** : Administrateur

Pour plus de détails, consultez : [ADMIN_SETUP.md](ADMIN_SETUP.md)

## 📱 Plateformes Supportées

- ✅ Web (Chrome, Firefox, Safari, Edge)
- ✅ Android
- ✅ iOS
- ✅ Windows Desktop
- ✅ macOS Desktop
- ✅ Linux Desktop

## 🛠️ Architecture

### Structure du Projet

```
lib/
├── constants/          # Constantes et styles
├── models/            # Modèles de données
├── pages/             # Pages de l'application
├── screens/           # Écrans spécialisés
├── services/          # Services et logique métier
├── widgets/           # Composants réutilisables
└── scripts/           # Scripts utilitaires
```

### Services Principaux

- **UserService** : Gestion des utilisateurs et authentification
- **ColisService** : Gestion des colis et livraisons
- **SyncService** : Synchronisation avec Firebase
- **FirebaseService** : Interface Firebase
- **NotificationService** : Notifications push

## 🔧 Développement

### Commandes Utiles

```bash
# Analyser le code
flutter analyze

# Formater le code
flutter format .

# Tests
flutter test

# Build pour production
flutter build web
flutter build apk
flutter build ios
```

### Scripts Disponibles

- `scripts/init_admin.dart` : Initialisation de l'administrateur
- `scripts/check_firebase_config.dart` : Vérification de la configuration

## 📚 Documentation

- [FIREBASE_SETUP.md](FIREBASE_SETUP.md) - Configuration Firebase
- [ADMIN_SETUP.md](ADMIN_SETUP.md) - Gestion administrateur
- [MODIFICATIONS_FACTURE.md](MODIFICATIONS_FACTURE.md) - Modifications factures
- [MODIFICATION_CARTES_FACTURES.md](MODIFICATION_CARTES_FACTURES.md) - Cartes factures
- [NOTIFICATION_LOGO_UPDATE.md](NOTIFICATION_LOGO_UPDATE.md) - Notifications

## 🤝 Contribution

1. Fork le projet
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 📞 Support

Pour toute question ou problème :

1. Consultez la documentation
2. Vérifiez les issues GitHub existantes
3. Créez une nouvelle issue si nécessaire

## 🔗 Liens Utiles

- [Repository GitHub](https://github.com/Wallymbx/G-n-ral-HCP-CRM.git)
- [Documentation Flutter](https://docs.flutter.dev/)
- [Documentation Firebase](https://firebase.google.com/docs)
- [Guide WhatsApp Business API](https://developers.facebook.com/docs/whatsapp)

---

**Développé avec ❤️ par l'équipe General HCP CRM**
