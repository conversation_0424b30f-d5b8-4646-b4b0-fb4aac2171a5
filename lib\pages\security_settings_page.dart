import 'package:flutter/material.dart';
import '../services/biometric_auth_service.dart';
import 'package:local_auth/local_auth.dart';

class SecuritySettingsPage extends StatefulWidget {
  const SecuritySettingsPage({super.key});

  @override
  State<SecuritySettingsPage> createState() => _SecuritySettingsPageState();
}

class _SecuritySettingsPageState extends State<SecuritySettingsPage> {
  final BiometricAuthService _biometricService = BiometricAuthService();
  bool _isBiometricEnabled = false;
  bool _isBiometricAvailable = false;
  List<BiometricType> _availableBiometrics = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSecuritySettings();
  }

  Future<void> _loadSecuritySettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final bool isEnabled = await _biometricService.isBiometricEnabled();
      final bool isAvailable = await _biometricService.isBiometricAvailable();
      final List<BiometricType> availableBiometrics = await _biometricService.getAvailableBiometrics();

      setState(() {
        _isBiometricEnabled = isEnabled;
        _isBiometricAvailable = isAvailable;
        _availableBiometrics = availableBiometrics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Erreur lors du chargement des paramètres de sécurité');
    }
  }

  Future<void> _toggleBiometricAuth(bool value) async {
    if (!_isBiometricAvailable) {
      _showErrorSnackBar('L\'authentification biométrique n\'est pas disponible sur cet appareil');
      return;
    }

    if (value) {
      // Activer l'authentification biométrique
      final bool isAuthenticated = await _biometricService.authenticate();
      if (isAuthenticated) {
        await _biometricService.setBiometricEnabled(true);
        setState(() {
          _isBiometricEnabled = true;
        });
        _showSuccessSnackBar('Authentification biométrique activée');
      } else {
        _showErrorSnackBar('Authentification échouée');
      }
    } else {
      // Désactiver l'authentification biométrique
      await _biometricService.setBiometricEnabled(false);
      setState(() {
        _isBiometricEnabled = false;
      });
      _showSuccessSnackBar('Authentification biométrique désactivée');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  String _getBiometricTypeDescription() {
    if (_availableBiometrics.isEmpty) {
      return 'Aucune authentification biométrique disponible';
    }

    List<String> types = [];
    for (BiometricType type in _availableBiometrics) {
      switch (type) {
        case BiometricType.fingerprint:
          types.add('Empreinte digitale');
          break;
        case BiometricType.face:
          types.add('Reconnaissance faciale');
          break;
        case BiometricType.iris:
          types.add('Reconnaissance de l\'iris');
          break;
        case BiometricType.weak:
          types.add('Authentification faible');
          break;
        case BiometricType.strong:
          types.add('Authentification forte');
          break;
      }
    }
    return types.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres de sécurité'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // Section Authentification biométrique
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.fingerprint,
                              color: Colors.blue[900],
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Authentification biométrique',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        // Switch pour activer/désactiver
                        SwitchListTile(
                          title: const Text('Activer l\'authentification biométrique'),
                          subtitle: Text(
                            _isBiometricEnabled
                                ? 'L\'authentification biométrique est activée'
                                : 'L\'authentification biométrique est désactivée',
                          ),
                          value: _isBiometricEnabled,
                          onChanged: _isBiometricAvailable ? _toggleBiometricAuth : null,
                          activeColor: Colors.blue[900],
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Informations sur les types disponibles
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Types d\'authentification disponibles :',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _getBiometricTypeDescription(),
                                style: TextStyle(
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        if (!_isBiometricAvailable) ...[
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.orange[50],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.orange[200]!),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.warning,
                                  color: Colors.orange[700],
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'L\'authentification biométrique n\'est pas disponible sur cet appareil.',
                                    style: TextStyle(
                                      color: Colors.orange[700],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Section Informations de sécurité
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.security,
                              color: Colors.blue[900],
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Informations de sécurité',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        _buildInfoTile(
                          Icons.info_outline,
                          'Protection des données',
                          'Vos données biométriques sont stockées de manière sécurisée sur votre appareil et ne sont jamais transmises.',
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildInfoTile(
                          Icons.lock_outline,
                          'Accès sécurisé',
                          'L\'authentification biométrique protège l\'accès à vos données sensibles de facturation.',
                        ),
                        
                        const SizedBox(height: 12),
                        
                        _buildInfoTile(
                          Icons.settings_backup_restore,
                          'Sauvegarde',
                          'Vous pouvez désactiver l\'authentification biométrique à tout moment depuis cette page.',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildInfoTile(IconData icon, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Colors.grey[600],
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}