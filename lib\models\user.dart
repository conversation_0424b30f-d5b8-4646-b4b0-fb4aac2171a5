class User {
  final String id;
  final String email;
  final String name;
  final String role; // 'admin', 'manager', 'employee'
  final String? phone;
  final String? avatar;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final bool isActive;
  final Map<String, dynamic> permissions;

  User({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    this.phone,
    this.avatar,
    required this.createdAt,
    required this.lastLoginAt,
    this.isActive = true,
    this.permissions = const {},
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      role: json['role'] ?? 'employee',
      phone: json['phone'],
      avatar: json['avatar'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] ?? DateTime.now().toIso8601String()),
      isActive: json['isActive'] ?? true,
      permissions: Map<String, dynamic>.from(json['permissions'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role,
      'phone': phone,
      'avatar': avatar,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'isActive': isActive,
      'permissions': permissions,
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? role,
    String? phone,
    String? avatar,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isActive,
    Map<String, dynamic>? permissions,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      permissions: permissions ?? this.permissions,
    );
  }

  bool hasPermission(String permission) {
    if (role == 'admin') return true;
    return permissions[permission] == true;
  }

  bool canManageUsers() => role == 'admin';
  bool canManageProducts() => role == 'admin' || role == 'manager' || hasPermission('manage_products');
  bool canManageInvoices() => role == 'admin' || role == 'manager' || hasPermission('manage_invoices');
  bool canManageColis() => role == 'admin' || role == 'manager' || hasPermission('manage_colis');
  bool canViewReports() => role == 'admin' || role == 'manager' || hasPermission('view_reports');
}