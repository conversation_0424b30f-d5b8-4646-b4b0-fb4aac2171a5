import 'package:flutter/material.dart';

/// Widget de dialogue pour la vérification du mot de passe
class PasswordDialog extends StatefulWidget {
  final String title;
  final String message;
  final String correctPassword;

  const PasswordDialog({
    super.key,
    required this.title,
    required this.message,
    required this.correctPassword,
  });

  @override
  State<PasswordDialog> createState() => _PasswordDialogState();

  /// Méthode statique pour afficher le dialogue facilement
  static Future<bool> show({
    required BuildContext context,
    required String title,
    required String message,
    String correctPassword = '03117455',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => PasswordDialog(
        title: title,
        message: message,
        correctPassword: correctPassword,
      ),
    );
    return result ?? false;
  }
}

class _PasswordDialogState extends State<PasswordDialog> {
  final TextEditingController _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  String? _errorMessage;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  void _verifyPassword() {
    final enteredPassword = _passwordController.text.trim();
    
    if (enteredPassword.isEmpty) {
      setState(() {
        _errorMessage = 'Veuillez saisir le mot de passe';
      });
      return;
    }

    if (enteredPassword == widget.correctPassword) {
      Navigator.of(context).pop(true);
    } else {
      setState(() {
        _errorMessage = 'Mot de passe incorrect';
      });
      _passwordController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(widget.message),
          const SizedBox(height: 16),
          TextField(
            controller: _passwordController,
            obscureText: !_isPasswordVisible,
            decoration: InputDecoration(
              labelText: 'Mot de passe',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
              errorText: _errorMessage,
            ),
            onSubmitted: (_) => _verifyPassword(),
            autofocus: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _verifyPassword,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('Confirmer'),
        ),
      ],
    );
  }
}