// EXEMPLE DE MODIFICATION POUR UTILISER LES ICÔNES PNG
// Remplacez le contenu de floating_navigation_bar.dart par ce code

import 'package:flutter/material.dart';

class FloatingNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const FloatingNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.blue[600],
        borderRadius: BorderRadius.circular(35),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(iconPath: 'assets/icons/navigation/dashboard.png', index: 0),
          _buildNavItem(iconPath: 'assets/icons/navigation/receipt.png', index: 1),
          _buildNavItem(iconPath: 'assets/icons/navigation/inventory.png', index: 2),
          _buildNavItem(iconPath: 'assets/icons/navigation/tasks.png', index: 3),
          _buildNavItem(
            iconPath: 'assets/icons/navigation/whatsapp.png',
            index: 4,
            color: const Color(0xFF25D366), // Couleur WhatsApp
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String iconPath,
    required int index,
    Color? color,
  }) {
    final bool isSelected = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Padding(
          padding: const EdgeInsets.all(13.0), // Padding pour ajuster la taille de l'icône
          child: Image.asset(
            iconPath,
            width: 24,
            height: 24,
            color: isSelected ? (color ?? Colors.black) : (color ?? Colors.white),
            // Pour les PNG avec transparence, utilisez ColorFilter.mode
            colorBlendMode: BlendMode.srcIn,
          ),
        ),
      ),
    );
  }
}